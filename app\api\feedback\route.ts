import { NextRequest } from "next/server";
import { getUserInfo } from "@/services/user";
import { createFeedback, validateFeedbackType, validateRating } from "@/services/feedback";
import { respData, respErr } from "@/lib/resp";

/**
 * POST /api/feedback
 * Submit user feedback
 */
export async function POST(req: NextRequest) {
  try {
    // Get user info (optional - anonymous feedback allowed)
    const userInfo = await getUserInfo();
    
    // Parse request body
    const { 
      feedback_type, 
      rating, 
      title, 
      content, 
      metadata,
      page_url 
    } = await req.json();

    // Validate required fields
    if (!feedback_type || typeof feedback_type !== 'string') {
      return respErr("Feedback type is required", 400);
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return respErr("Feedback content is required", 400);
    }

    // Validate feedback type
    if (!validateFeedbackType(feedback_type)) {
      return respErr("Invalid feedback type", 400);
    }

    // Validate rating if provided
    if (rating !== undefined && !validateRating(rating)) {
      return respErr("Rating must be between 1 and 5", 400);
    }

    // Validate content length
    if (content.length > 5000) {
      return respErr("Feedback content is too long (max 5000 characters)", 400);
    }

    // Validate title length if provided
    if (title && title.length > 255) {
      return respErr("Title is too long (max 255 characters)", 400);
    }

    // Get client information
    const user_agent = req.headers.get("user-agent") || undefined;
    const forwarded = req.headers.get("x-forwarded-for");
    const ip_address = forwarded ? forwarded.split(",")[0] : req.ip || undefined;

    // Create feedback
    const feedback = await createFeedback({
      user_uuid: userInfo?.uuid,
      user_email: userInfo?.email,
      feedback_type,
      rating,
      title: title?.trim(),
      content: content.trim(),
      metadata,
      user_agent,
      ip_address,
      page_url,
    });

    return respData({
      success: true,
      message: "Feedback submitted successfully",
      feedback_uuid: feedback.uuid,
    });

  } catch (error) {
    console.error("Submit feedback error:", error);
    return respErr("Failed to submit feedback", 500);
  }
}

/**
 * GET /api/feedback
 * Get user's own feedback (requires authentication)
 */
export async function GET(req: NextRequest) {
  try {
    // Get user info (required for viewing own feedback)
    const userInfo = await getUserInfo();
    if (!userInfo || !userInfo.uuid) {
      return respErr("Authentication required", 401);
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return respErr("Invalid pagination parameters", 400);
    }

    // Get user's feedback
    const { getFeedbacksByUserUuid } = await import("@/models/feedback");
    const feedbacks = await getFeedbacksByUserUuid(userInfo.uuid, page, limit);

    return respData({
      feedbacks,
      page,
      limit,
      total: feedbacks.length,
    });

  } catch (error) {
    console.error("Get user feedback error:", error);
    return respErr("Failed to get feedback", 500);
  }
}
