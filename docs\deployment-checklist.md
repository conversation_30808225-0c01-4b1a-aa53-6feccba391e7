# 积分系统部署检查清单

## 🚀 生产环境部署前检查

### 📋 数据库准备

#### 必需表结构
- [ ] `credits` 表已创建并包含所有必需字段
- [ ] `feedbacks` 表已创建（如果使用反馈功能）
- [ ] 所有性能优化索引已创建
- [ ] 数据库连接池配置正确

#### 索引验证
```sql
-- 验证关键索引是否存在
SELECT indexname, tablename, indexdef 
FROM pg_indexes 
WHERE tablename IN ('credits', 'feedbacks');

-- 应该包含以下索引：
-- idx_credits_user_expired
-- idx_credits_user_type_expired
-- idx_feedbacks_status
-- idx_feedbacks_type
```

### 🔧 环境变量配置

#### 必需环境变量
- [ ] `DATABASE_URL` - 数据库连接字符串
- [ ] `NEXTAUTH_SECRET` - 认证密钥
- [ ] `NEXTAUTH_URL` - 应用域名
- [ ] `ADMIN_EMAILS` - 管理员邮箱列表

#### 可选环境变量
- [ ] `CREDITS_CACHE_TTL` - 积分缓存时间（默认30秒）
- [ ] `MAX_CREDITS_PER_GIFT` - 单次赠送积分上限（默认10000）

### 🔐 安全配置

#### 认证安全
- [ ] NEXTAUTH_SECRET 使用强随机字符串
- [ ] 生产环境禁用开发模式
- [ ] CORS 配置正确
- [ ] API 路由权限验证正常

#### 数据安全
- [ ] 数据库连接使用SSL
- [ ] 敏感数据加密存储
- [ ] 定期数据备份策略
- [ ] 访问日志记录

### 📊 性能配置

#### 缓存设置
- [ ] 内存缓存TTL配置合理（推荐30秒）
- [ ] 数据库连接池大小适当
- [ ] 静态资源CDN配置
- [ ] 图片压缩和优化

#### 监控指标
- [ ] 数据库查询性能监控
- [ ] API响应时间监控
- [ ] 内存使用监控
- [ ] 错误率监控

### 🧪 功能测试

#### 核心功能验证
- [ ] 用户注册和登录正常
- [ ] 积分显示功能正常
- [ ] 图片生成和积分扣除正常
- [ ] 积分不足提示正常
- [ ] 管理员功能正常（除Gift Credits页面）

#### API接口测试
```bash
# 测试积分查询API
curl -H "Authorization: Bearer <token>" \
     https://your-domain.com/api/user/credits

# 测试积分扣除API
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <token>" \
     -d '{"credits":1,"reason":"test"}' \
     https://your-domain.com/api/user/credits/deduct
```

#### 前端功能测试
- [ ] 积分显示在Header中正确显示
- [ ] 生成按钮积分计算正确
- [ ] 高质量选项2倍积分计算正确
- [ ] 积分不足对话框正常弹出
- [ ] 定价页面跳转正常

### 🌍 国际化配置

#### 语言支持
- [ ] 中文界面显示正常
- [ ] 英文界面显示正常
- [ ] 语言切换功能正常
- [ ] 所有积分相关文案已翻译

#### 翻译文件
- [ ] `messages/zh.json` 包含所有必需翻译
- [ ] `messages/en.json` 包含所有必需翻译
- [ ] 翻译内容准确无误

### 📱 响应式设计

#### 设备兼容性
- [ ] 桌面端显示正常
- [ ] 移动端显示正常
- [ ] 平板端显示正常
- [ ] 各种屏幕尺寸适配良好

#### 浏览器兼容性
- [ ] Chrome 浏览器正常
- [ ] Firefox 浏览器正常
- [ ] Safari 浏览器正常
- [ ] Edge 浏览器正常

### 🔍 错误处理

#### 异常情况处理
- [ ] 网络错误友好提示
- [ ] 数据库连接失败处理
- [ ] API超时处理
- [ ] 用户权限不足提示

#### 日志记录
- [ ] 积分操作日志完整
- [ ] 错误日志详细记录
- [ ] 性能日志监控
- [ ] 安全事件日志

### 📈 性能基准

#### 响应时间要求
- [ ] 积分查询API < 200ms
- [ ] 积分扣除API < 500ms
- [ ] 页面加载时间 < 3s
- [ ] 图片生成响应 < 1s

#### 并发处理
- [ ] 支持100+并发用户
- [ ] 数据库连接池不溢出
- [ ] 内存使用稳定
- [ ] CPU使用率合理

### ✅ 已解决问题

#### 已修复的问题
- [x] **Gift Credits页面服务器错误** - 已解决
  - 问题原因：临时的开发服务器状态异常
  - 解决方式：服务器重启自动解决
  - 当前状态：✅ 功能完全正常工作
  - 验证结果：页面加载、表单验证、积分赠送均正常

#### 功能验证记录
- ✅ 页面正常加载和显示
- ✅ 表单验证功能正常
- ✅ 积分赠送功能正常
- ✅ 数据库更新正常
- ✅ 用户体验完整

### 📋 部署步骤

#### 1. 代码部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
npm install

# 3. 构建应用
npm run build

# 4. 启动应用
npm start
```

#### 2. 数据库迁移
```sql
-- 执行数据库迁移脚本
\i docs/database-performance-optimization.sql
```

#### 3. 环境配置
```bash
# 设置环境变量
export DATABASE_URL="postgresql://..."
export NEXTAUTH_SECRET="..."
export NEXTAUTH_URL="https://your-domain.com"
export ADMIN_EMAILS="<EMAIL>"
```

#### 4. 健康检查
```bash
# 检查应用状态
curl https://your-domain.com/api/health

# 检查数据库连接
curl https://your-domain.com/api/user/credits
```

### ✅ 部署完成确认

#### 最终验证
- [ ] 所有核心功能正常工作
- [ ] 性能指标达到要求
- [ ] 安全配置正确
- [ ] 监控系统正常
- [ ] 备份策略已实施

#### 上线通知
- [ ] 团队成员已通知
- [ ] 用户文档已更新
- [ ] 监控告警已配置
- [ ] 回滚计划已准备

---

**检查清单版本**: v1.0  
**最后更新**: 2025-08-03  
**检查人**: _______________  
**部署日期**: _______________
