import { getUserValidCredits, insertCredit, getBatchUserValidCredits } from "@/models/credit";

import { Credit } from "@/types/credit";
import { UserCredits } from "@/types/user";
import { getFirstPaidOrderByUserUuid, getBatchFirstPaidOrders } from "@/models/order";
import { getIsoTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  ImageGeneration = "image_generation", // image generation consumption
  GenerationRefund = "generation_refund", // generation failed refund
  AdminGift = "admin_gift", // admin gift credits
}

export enum CreditsAmount {
  NewUserGet = 10,
  PingCost = 1,
  ImageGenerationBase = 1, // base cost per image
}

// Image generation settings interface
export interface GenerationSettings {
  imageCount: number;
  highQuality: boolean;
}

// Credit calculation result interface
export interface CreditCalculation {
  totalCredits: number;
  baseCredits: number;
  qualityMultiplier: number;
  imageCount: number;
}

// 内存缓存，用于短期缓存积分信息
const creditsCache = new Map<string, { data: UserCredits; timestamp: number }>();
const CACHE_TTL = 30 * 1000; // 30秒缓存

export async function getUserCredits(user_uuid: string, useCache: boolean = true): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    // 检查缓存
    if (useCache) {
      const cached = creditsCache.get(user_uuid);
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data;
      }
    }

    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    if (credits) {
      credits.forEach((v: Credit) => {
        user_credits.left_credits += v.credits;
      });
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    if (user_credits.left_credits > 0) {
      user_credits.is_pro = true;
    }

    // 更新缓存
    if (useCache) {
      creditsCache.set(user_uuid, {
        data: user_credits,
        timestamp: Date.now(),
      });
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

/**
 * 批量获取多个用户的积分信息，优化N+1查询问题
 */
export async function getBatchUserCredits(user_uuids: string[]): Promise<Map<string, UserCredits>> {
  const result = new Map<string, UserCredits>();

  if (user_uuids.length === 0) {
    return result;
  }

  try {
    // 批量获取所有用户的有效积分
    const batchCredits = await getBatchUserValidCredits(user_uuids);

    // 批量获取首次付费订单信息
    const batchFirstOrders = await getBatchFirstPaidOrders(user_uuids);

    // 为每个用户计算积分
    for (const user_uuid of user_uuids) {
      let user_credits: UserCredits = {
        left_credits: 0,
      };

      // 检查是否有首次付费订单
      if (batchFirstOrders.has(user_uuid)) {
        user_credits.is_recharged = true;
      }

      // 计算积分总和
      const userCredits = batchCredits.get(user_uuid) || [];
      userCredits.forEach((credit: Credit) => {
        user_credits.left_credits += credit.credits;
      });

      if (user_credits.left_credits < 0) {
        user_credits.left_credits = 0;
      }

      if (user_credits.left_credits > 0) {
        user_credits.is_pro = true;
      }

      result.set(user_uuid, user_credits);

      // 更新缓存
      creditsCache.set(user_uuid, {
        data: user_credits,
        timestamp: Date.now(),
      });
    }

    return result;
  } catch (e) {
    console.log("get batch user credits failed: ", e);

    // 如果批量查询失败，回退到单个查询
    for (const user_uuid of user_uuids) {
      try {
        const credits = await getUserCredits(user_uuid, false);
        result.set(user_uuid, credits);
      } catch (error) {
        console.log(`Failed to get credits for user ${user_uuid}:`, error);
        result.set(user_uuid, { left_credits: 0 });
      }
    }

    return result;
  }
}

/**
 * 清除用户积分缓存
 */
export function clearUserCreditsCache(user_uuid?: string) {
  if (user_uuid) {
    creditsCache.delete(user_uuid);
  } else {
    creditsCache.clear();
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    const userCredits = await getUserValidCredits(user_uuid);
    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        left_credits += credit.credits;

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no;
          expired_at = credit.expired_at || "";
          break;
        }

        // look for next credit
      }
    }

    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits,
      order_no: order_no,
      expired_at: expired_at,
    };
    await insertCredit(new_credit);

    // 清除用户积分缓存
    clearUserCreditsCache(user_uuid);
  } catch (e) {
    console.log("decrease credits failed: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no || "",
      expired_at: expired_at || "",
    };
    await insertCredit(new_credit);

    // 清除用户积分缓存
    clearUserCreditsCache(user_uuid);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

/**
 * Calculate credits required for image generation
 * Formula: baseCredits × imageCount × qualityMultiplier
 */
export function calculateImageGenerationCredits(settings: GenerationSettings): CreditCalculation {
  const baseCredits = CreditsAmount.ImageGenerationBase;
  const qualityMultiplier = settings.highQuality ? 2 : 1;
  const totalCredits = baseCredits * settings.imageCount * qualityMultiplier;

  return {
    totalCredits,
    baseCredits,
    qualityMultiplier,
    imageCount: settings.imageCount,
  };
}

/**
 * Check if user has sufficient credits for generation
 */
export async function checkSufficientCredits(
  user_uuid: string,
  requiredCredits: number
): Promise<{ sufficient: boolean; currentCredits: number }> {
  try {
    const userCredits = await getUserCredits(user_uuid);

    // 正常的积分检查逻辑

    return {
      sufficient: userCredits.left_credits >= requiredCredits,
      currentCredits: userCredits.left_credits,
    };
  } catch (e) {
    console.log("check sufficient credits failed: ", e);
    return {
      sufficient: false,
      currentCredits: 0,
    };
  }
}

/**
 * Safely deduct credits with transaction support and rollback capability
 * Returns transaction ID for potential rollback operations
 */
export async function deductCreditsWithTransaction({
  user_uuid,
  trans_type,
  credits,
  description,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
  description?: string;
}): Promise<{ success: boolean; transactionId: string; remainingCredits: number }> {
  try {
    // First check if user has sufficient credits
    const creditCheck = await checkSufficientCredits(user_uuid, credits);
    if (!creditCheck.sufficient) {
      throw new Error(`Insufficient credits. Required: ${credits}, Available: ${creditCheck.currentCredits}`);
    }

    // Generate transaction ID for tracking
    const transactionId = getSnowId();

    // Find the appropriate credit source for deduction
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    const userCredits = await getUserValidCredits(user_uuid);
    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        left_credits += credit.credits;

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no;
          expired_at = credit.expired_at || "";
          break;
        }
      }
    }

    // Create deduction record
    const new_credit: Credit = {
      trans_no: transactionId,
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits, // Negative value for deduction
      order_no: order_no,
      expired_at: expired_at,
    };

    // Insert the credit deduction record
    await insertCredit(new_credit);

    // Clear user credits cache
    clearUserCreditsCache(user_uuid);

    // Calculate remaining credits after deduction
    const remainingCredits = creditCheck.currentCredits - credits;

    return {
      success: true,
      transactionId,
      remainingCredits,
    };

  } catch (e) {
    console.log("deduct credits with transaction failed: ", e);
    throw e;
  }
}

/**
 * Refund credits by creating a positive credit entry
 * Used for rollback operations when image generation fails
 */
export async function refundCredits({
  user_uuid,
  credits,
  originalTransactionId,
  reason,
}: {
  user_uuid: string;
  credits: number;
  originalTransactionId: string;
  reason?: string;
}): Promise<{ success: boolean; refundTransactionId: string }> {
  try {
    // Generate refund transaction ID
    const refundTransactionId = getSnowId();

    // Create refund record
    const refund_credit: Credit = {
      trans_no: refundTransactionId,
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: CreditsTransType.GenerationRefund,
      credits: credits, // Positive value for refund
      order_no: `REFUND_${originalTransactionId}`,
      expired_at: undefined, // Refunded credits don't expire
    };

    // Insert the refund record
    await insertCredit(refund_credit);

    // Clear user credits cache
    clearUserCreditsCache(user_uuid);

    return {
      success: true,
      refundTransactionId,
    };

  } catch (e) {
    console.log("refund credits failed: ", e);
    throw e;
  }
}
