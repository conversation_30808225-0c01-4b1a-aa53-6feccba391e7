"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { GenerationResult, GenerationQueue, ImageResultsConfig, GenerationSettings } from "@/types/image-results";
import ImageResultContainer from "./ImageResultContainer";
import { getUuid } from "@/lib/hash";

interface ImageResultsManagerProps {
  config?: ImageResultsConfig;
  className?: string;
}

export function useImageResultsManager({
  config = {
    enableCountdown: true,
    countdownDuration: 10,
    maxRetries: 3,
    retryDelay: 5,
  },
}: { config?: ImageResultsConfig } = {}) {
  // 测试数据
  const getTestData = (): GenerationResult[] => [
    // 已完成 - 4张图片
    {
      id: "test-1",
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
      prompt: "一只可爱的橘猫在阳光明媚的花园里玩耍，周围有五颜六色的花朵",
      settings: {
        provider: "openai",
        model: "dall-e-3",
        ratio: "1:1",
        style: "vivid",
        color: "warm",
        lighting: "natural",
        composition: "centered",
        enableHighQuality: true,
      },
      images: [
        {
          url: "https://picsum.photos/512/512?random=1",
          filename: "cute-cat-garden-1.png",
          size: 524288,
          width: 512,
          height: 512,
        },
        {
          url: "https://picsum.photos/512/512?random=2",
          filename: "cute-cat-garden-2.png",
          size: 498432,
          width: 512,
          height: 512,
        },
        {
          url: "https://picsum.photos/512/512?random=3",
          filename: "cute-cat-garden-3.png",
          size: 512000,
          width: 512,
          height: 512,
        },
        {
          url: "https://picsum.photos/512/512?random=4",
          filename: "cute-cat-garden-4.png",
          size: 487424,
          width: 512,
          height: 512,
        },
      ],
      status: "completed" as const,
      mode: "text-to-image" as const,
    },
    // 生成中 - 无图片
    {
      id: "test-2",
      timestamp: new Date(Date.now() - 30 * 1000), // 30秒前
      prompt: "未来科技城市的夜景，霓虹灯闪烁，飞行汽车穿梭其中",
      settings: {
        provider: "replicate",
        model: "sdxl",
        ratio: "16:9",
        style: "cyberpunk",
        color: "cool",
        lighting: "neon",
        composition: "wide",
        enableHighQuality: false,
      },
      images: [],
      status: "generating" as const,
      mode: "text-to-image" as const,
    },
    // 已完成 - 2张图片
    {
      id: "test-3",
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15分钟前
      prompt: "古典油画风格的山水画，有瀑布和古松",
      settings: {
        provider: "openai",
        model: "dall-e-3",
        ratio: "4:3",
        style: "classical",
        color: "natural",
        lighting: "soft",
        composition: "landscape",
        enableHighQuality: true,
      },
      images: [
        {
          url: "https://picsum.photos/640/480?random=5",
          filename: "classical-landscape-1.png",
          size: 768000,
          width: 640,
          height: 480,
        },
        {
          url: "https://picsum.photos/640/480?random=6",
          filename: "classical-landscape-2.png",
          size: 742400,
          width: 640,
          height: 480,
        },
      ],
      status: "completed" as const,
      mode: "text-to-image" as const,
    },
    // 失败状态
    {
      id: "test-4",
      timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2分钟前
      prompt: "不合适的内容提示词测试",
      settings: {
        provider: "openai",
        model: "dall-e-3",
        ratio: "1:1",
        style: "natural",
        enableHighQuality: false,
      },
      images: [],
      status: "failed" as const,
      mode: "text-to-image" as const,
      error: "内容违反了使用政策，请修改提示词后重试",
    },
    // 图片转图片模式 - 已完成 - 1张图片
    {
      id: "test-5",
      timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8分钟前
      prompt: "将这张图片转换为水彩画风格",
      settings: {
        provider: "replicate",
        model: "sdxl",
        ratio: "1:1",
        style: "watercolor",
        color: "soft",
        lighting: "natural",
        composition: "original",
        enableHighQuality: true,
      },
      images: [
        {
          url: "https://picsum.photos/512/512?random=7",
          filename: "watercolor-style.png",
          size: 445440,
          width: 512,
          height: 512,
        },
      ],
      status: "completed" as const,
      mode: "image-to-image" as const,
      uploadedImage: "https://picsum.photos/512/512?random=8", // 原始上传图片
    },
  ];

  const [results, setResults] = useState<GenerationResult[]>(() => getTestData());
  const [queue, setQueue] = useState<GenerationQueue[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const processingRef = useRef(false);
  const queueRef = useRef<GenerationQueue[]>([]);

  // Generate image function
  const generateImage = useCallback(async (
    prompt: string,
    settings: GenerationSettings,
    mode: 'text-to-image' | 'image-to-image' = 'text-to-image',
    uploadedImage?: string
  ) => {
    const queueItem: GenerationQueue = {
      id: getUuid(),
      prompt,
      settings,
      mode,
      uploadedImage,
      countdown: config.countdownDuration || 10,
    };

    // Add to queue
    queueRef.current = [...queueRef.current, queueItem];
    setQueue([...queueRef.current]);

    // Start processing if not already processing
    if (!processingRef.current) {
      processQueue();
    }

    return queueItem.id;
  }, [config.countdownDuration]);

  // Process queue
  const processQueue = useCallback(async () => {
    if (processingRef.current) return;

    processingRef.current = true;
    setIsProcessing(true);

    // Process queue items one by one using ref
    while (queueRef.current.length > 0) {
      const currentItem = queueRef.current[0];

      // Remove from queue
      queueRef.current = queueRef.current.slice(1);
      setQueue([...queueRef.current]);

      // Create result entry with image slots
      const result: GenerationResult = {
        id: currentItem.id,
        timestamp: new Date(),
        prompt: currentItem.prompt,
        settings: currentItem.settings,
        images: [],
        status: 'waiting',
        mode: currentItem.mode,
        uploadedImage: currentItem.uploadedImage,
        countdown: currentItem.countdown,
        stage: 'waiting',
        imageSlots: [
          { index: 0, status: 'waiting' },
          { index: 1, status: 'waiting' },
          { index: 2, status: 'waiting' },
          { index: 3, status: 'waiting' },
        ],
        currentGeneratingIndex: 0,
      };

      // Add to results (at the beginning for newest first)
      setResults(prev => [result, ...prev]);

      try {
        // Generate images one by one (4 images total)
        const generatedImages: any[] = [];

        for (let imageIndex = 0; imageIndex < 4; imageIndex++) {
          // Update current generating index
          setResults(prev => prev.map(r =>
            r.id === result.id ? { ...r, currentGeneratingIndex: imageIndex } : r
          ));

          // Countdown for this specific image
          if (config.enableCountdown && currentItem.countdown && currentItem.countdown > 0) {
            // Update this slot to countdown status
            setResults(prev => prev.map(r =>
              r.id === result.id ? {
                ...r,
                imageSlots: r.imageSlots?.map(slot =>
                  slot.index === imageIndex
                    ? { ...slot, status: 'countdown' as const, countdown: currentItem.countdown }
                    : slot
                ) || []
              } : r
            ));

            // Countdown with real-time updates for this slot
            for (let i = currentItem.countdown; i > 0; i--) {
              setResults(prev => prev.map(r =>
                r.id === result.id ? {
                  ...r,
                  imageSlots: r.imageSlots?.map(slot =>
                    slot.index === imageIndex
                      ? { ...slot, countdown: i }
                      : slot
                  ) || []
                } : r
              ));
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }

          // Update this slot to generating status
          setResults(prev => prev.map(r =>
            r.id === result.id ? {
              ...r,
              status: 'generating' as const,
              imageSlots: r.imageSlots?.map(slot =>
                slot.index === imageIndex
                  ? { ...slot, status: 'generating' as const, countdown: 0 }
                  : slot
              ) || []
            } : r
          ));

          // Make API call for single image with credit deduction
          const response = await fetch('/api/generate/image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: currentItem.prompt,
              provider: currentItem.settings.provider || 'openai',
              model: currentItem.settings.model || 'dall-e-3',
              imageCount: 1, // Generate one image at a time
              highQuality: currentItem.settings.highQuality || false,
              ...currentItem.settings,
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data && data.data.images && data.data.images.length > 0) {
            const imageData = data.data.images[0]; // Get the first (and only) image
            generatedImages.push(imageData);

            // Update this slot to completed and add the image
            setResults(prev => prev.map(r =>
              r.id === result.id ? {
                ...r,
                images: [...r.images, imageData],
                imageSlots: r.imageSlots?.map(slot =>
                  slot.index === imageIndex
                    ? { ...slot, status: 'completed' as const, image: imageData }
                    : slot
                ) || []
              } : r
            ));
          } else {
            throw new Error(data.message || `Failed to generate image ${imageIndex + 1}`);
          }

          // Small delay between images
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Mark the entire result as completed
        setResults(prev => prev.map(r =>
          r.id === result.id ? { ...r, status: 'completed' as const } : r
        ));

      } catch (error) {
        console.error('Generation failed:', error);

        // Update with error
        setResults(prev => prev.map(r =>
          r.id === result.id
            ? {
                ...r,
                status: 'failed' as const,
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : r
        ));
      }

      // Small delay between queue items
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    processingRef.current = false;
    setIsProcessing(false);
  }, [config.enableCountdown]);

  // Update queue processing when queue changes
  useEffect(() => {
    const processQueueAsync = async () => {
      if (queueRef.current.length > 0 && !processingRef.current) {
        processQueue();
      }
    };
    processQueueAsync();
  }, [processQueue]);

  // Cancel generation
  const cancelGeneration = useCallback((resultId: string) => {
    // Remove from queue if exists
    queueRef.current = queueRef.current.filter(item => item.id !== resultId);
    setQueue([...queueRef.current]);

    // Update result status
    setResults(prev => prev.map(r =>
      r.id === resultId && (r.status === 'waiting' || r.status === 'generating')
        ? { ...r, status: 'failed' as const, error: 'Cancelled by user' }
        : r
    ));
  }, []);

  return {
    results,
    setResults,
    queue,
    isProcessing,
    generateImage,
    cancelGeneration,
  };
}

// 展示组件
export default function ImageResultsDisplay({
  results,
  config,
  onCancel,
  className,
}: {
  results: GenerationResult[];
  config?: ImageResultsConfig;
  onCancel?: (id: string) => void;
  className?: string;
}) {
  return (
    <div className={className}>
      {results.map((result) => (
        <div key={result.id} className="mb-6">
          <ImageResultContainer
            result={result}
            enableCountdown={config?.enableCountdown}
            onCancel={() => onCancel?.(result.id)}
          />
        </div>
      ))}
    </div>
  );
}
