"use client";

import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { User } from "@/types/user";
import moment from "moment";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useTranslations } from "next-intl";

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchEmail, setSearchEmail] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const tCredits = useTranslations("credits.admin");

  // 获取用户数据
  const fetchUsers = async (email?: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/users?${email ? `email=${encodeURIComponent(email)}` : ''}`);
      if (response.ok) {
        const result = await response.json();
        // API返回格式: {code: 0, data: {users: [...], page, limit, searchEmail}}
        if (result.code === 0 && result.data) {
          setUsers(result.data.users || []);
        } else {
          console.error("API returned error:", result.message);
          setUsers([]);
        }
      } else {
        console.error("Failed to fetch users");
        setUsers([]);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchUsers();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setSearchEmail(searchInput);
    fetchUsers(searchInput);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchInput("");
    setSearchEmail("");
    fetchUsers();
  };

  // 按Enter键搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const columns: TableColumn[] = [
    { name: "uuid", title: "UUID", type: "copy" },
    { name: "email", title: "Email" },
    { name: "nickname", title: "Name" },
    {
      name: "avatar_url",
      title: "Avatar",
      callback: (row) => (
        <img src={row.avatar_url} className="w-10 h-10 rounded-full" />
      ),
    },
    {
      name: "credits",
      title: tCredits("credits_column"),
      callback: (row) => {
        const credits = row.credits?.left_credits || 0;
        const isPro = row.credits?.is_pro || false;
        const isRecharged = row.credits?.is_recharged || false;

        return (
          <div className="flex items-center gap-2">
            <span className={`font-medium ${credits > 0 ? 'text-green-600' : 'text-gray-500'}`}>
              {credits}
            </span>
            {isPro && (
              <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                Pro
              </span>
            )}
            {isRecharged && (
              <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                Paid
              </span>
            )}
          </div>
        );
      },
    },
    {
      name: "created_at",
      title: "Created At",
      callback: (row) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      name: "actions",
      title: tCredits("actions_column"),
      callback: (row) => (
        <div className="flex gap-2">
          <Button
            onClick={() => window.location.href = `/admin/credits/gift?email=${encodeURIComponent(row.email)}`}
            variant="outline"
            size="sm"
          >
            {tCredits("gift_action")}
          </Button>
        </div>
      ),
    },
  ];

  const table: TableSlotType = {
    title: "All Users",
    columns,
    data: users,
    empty_message: loading ? "Loading..." : searchEmail ? `No users found for "${searchEmail}"` : "No users found",
  };

  return (
    <>
      {/* 使用自定义Header */}
      <div className="w-full px-4 md:px-8 py-8">
        <h1 className="text-2xl font-medium mb-8">All Users</h1>

        {/* 搜索栏和操作按钮 */}
        <div className="mb-6 flex gap-2 items-center justify-between">
          <div className="flex gap-2 items-center max-w-md">
            <Input
              placeholder="Search by email..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button onClick={handleSearch} size="sm">
              <Search className="w-4 h-4 mr-1" />
              Search
            </Button>
            {searchEmail && (
              <Button onClick={handleReset} variant="outline" size="sm">
                Reset
              </Button>
            )}
          </div>

          {/* 管理员操作按钮 */}
          <div className="flex gap-2">
            <Button
              onClick={() => window.location.href = '/admin/credits/gift'}
              variant="outline"
              size="sm"
            >
              {tCredits("gift_credits_action")}
            </Button>
          </div>
        </div>

        {/* 表格 */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr>
                {columns.map((column, idx) => (
                  <th key={idx} className="text-left p-2 border-b">
                    {column.title}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={columns.length} className="text-center p-4">
                    Loading...
                  </td>
                </tr>
              ) : users.length > 0 ? (
                users.map((user, idx) => (
                  <tr key={idx} className="border-b">
                    {columns.map((column, cidx) => (
                      <td key={cidx} className="p-2">
                        {column.callback ? column.callback(user) : user[column.name as keyof User]}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={columns.length} className="text-center p-4">
                    {searchEmail ? `No users found for "${searchEmail}"` : "No users found"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}
