"use client";

import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { Feedback } from "@/types/feedback";
import moment from "moment";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, MessageSquare, Star, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

interface FeedbackWithActions extends Feedback {
  actions?: any;
}

interface FeedbackStats {
  total: number;
  pending: number;
  in_progress: number;
  resolved: number;
  closed: number;
  rejected: number;
}

export default function AdminFeedbackPage() {
  const [feedbacks, setFeedbacks] = useState<FeedbackWithActions[]>([]);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const [replyDialogOpen, setReplyDialogOpen] = useState(false);
  const [adminReply, setAdminReply] = useState("");
  const [newStatus, setNewStatus] = useState("");
  const [updating, setUpdating] = useState(false);

  // 获取反馈列表
  const fetchFeedbacks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: "1",
        limit: "100",
        stats: "true",
      });
      
      if (statusFilter) params.append("status", statusFilter);
      if (typeFilter) params.append("feedback_type", typeFilter);

      const response = await fetch(`/api/admin/feedback?${params}`);
      const data = await response.json();

      if (data.code === 0) {
        setFeedbacks(data.data.feedbacks || []);
        setStats(data.data.stats || null);
      } else {
        toast.error(data.message || "Failed to load feedbacks");
      }
    } catch (error) {
      console.error("Failed to fetch feedbacks:", error);
      toast.error("Failed to load feedbacks");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedbacks();
  }, [statusFilter, typeFilter]);

  // 处理反馈回复和状态更新
  const handleUpdateFeedback = async () => {
    if (!selectedFeedback || !newStatus) {
      toast.error("Please select a status");
      return;
    }

    try {
      setUpdating(true);
      const response = await fetch("/api/admin/feedback", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          uuid: selectedFeedback.uuid,
          status: newStatus,
          admin_reply: adminReply.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        toast.success("Feedback updated successfully");
        setReplyDialogOpen(false);
        setSelectedFeedback(null);
        setAdminReply("");
        setNewStatus("");
        fetchFeedbacks(); // 重新加载数据
      } else {
        toast.error(data.message || "Failed to update feedback");
      }
    } catch (error) {
      console.error("Failed to update feedback:", error);
      toast.error("Failed to update feedback");
    } finally {
      setUpdating(false);
    }
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "Pending", variant: "secondary" as const, icon: Clock },
      in_progress: { label: "In Progress", variant: "default" as const, icon: AlertCircle },
      resolved: { label: "Resolved", variant: "default" as const, icon: CheckCircle },
      closed: { label: "Closed", variant: "outline" as const, icon: XCircle },
      rejected: { label: "Rejected", variant: "destructive" as const, icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  // 获取反馈类型徽章
  const getTypeBadge = (type: string) => {
    const typeConfig = {
      bug: { label: "Bug", variant: "destructive" as const },
      feature: { label: "Feature", variant: "default" as const },
      general: { label: "General", variant: "secondary" as const },
      complaint: { label: "Complaint", variant: "destructive" as const },
      suggestion: { label: "Suggestion", variant: "default" as const },
      praise: { label: "Praise", variant: "default" as const },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.general;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  // 渲染评分
  const renderRating = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    );
  };

  const columns: TableColumn[] = [
    {
      name: "feedback_type",
      title: "Type",
      callback: (row) => getTypeBadge(row.feedback_type),
    },
    {
      name: "title",
      title: "Title",
      callback: (row) => (
        <div className="max-w-xs">
          <div className="font-medium truncate">{row.title || "No title"}</div>
          <div className="text-sm text-gray-500 truncate">{row.content}</div>
        </div>
      ),
    },
    {
      name: "user_email",
      title: "User",
      callback: (row) => row.user_email || "Anonymous",
    },
    {
      name: "rating",
      title: "Rating",
      callback: (row) => renderRating(row.rating),
    },
    {
      name: "status",
      title: "Status",
      callback: (row) => getStatusBadge(row.status),
    },
    {
      name: "created_at",
      title: "Created",
      callback: (row) => moment(row.created_at).format("MM-DD HH:mm"),
    },
    {
      name: "actions",
      title: "Actions",
      callback: (row) => (
        <Button
          onClick={() => {
            setSelectedFeedback(row);
            setAdminReply(row.admin_reply || "");
            setNewStatus(row.status);
            setReplyDialogOpen(true);
          }}
          variant="outline"
          size="sm"
        >
          <MessageSquare className="w-4 h-4 mr-1" />
          Manage
        </Button>
      ),
    },
  ];

  // 过滤反馈数据
  const filteredFeedbacks = feedbacks.filter((feedback) => {
    if (statusFilter && statusFilter !== "all" && feedback.status !== statusFilter) return false;
    if (typeFilter && typeFilter !== "all" && feedback.feedback_type !== typeFilter) return false;
    return true;
  });

  const table: TableSlotType = {
    title: "Feedback Management",
    columns,
    data: filteredFeedbacks,
    empty_message: loading ? "Loading..." : "No feedback found",
  };

  return (
    <>
      <div className="w-full px-4 md:px-8 py-8">
        <h1 className="text-2xl font-medium mb-8">Feedback Management</h1>

        {/* 统计信息 */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{stats.in_progress}</div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
              <div className="text-sm text-gray-600">Resolved</div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="text-2xl font-bold text-gray-600">{stats.closed}</div>
              <div className="text-sm text-gray-600">Closed</div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
              <div className="text-sm text-gray-600">Rejected</div>
            </div>
          </div>
        )}

        {/* 过滤器 */}
        <div className="mb-6 flex gap-4 items-center">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="bug">Bug</SelectItem>
              <SelectItem value="feature">Feature</SelectItem>
              <SelectItem value="general">General</SelectItem>
              <SelectItem value="complaint">Complaint</SelectItem>
              <SelectItem value="suggestion">Suggestion</SelectItem>
              <SelectItem value="praise">Praise</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={fetchFeedbacks} variant="outline" size="sm">
            <Search className="w-4 h-4 mr-1" />
            Refresh
          </Button>
        </div>

        {/* 表格 */}
        <TableSlot table={table} />

        {/* 回复对话框 */}
        <Dialog open={replyDialogOpen} onOpenChange={setReplyDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Manage Feedback</DialogTitle>
            </DialogHeader>
            
            {selectedFeedback && (
              <div className="space-y-4">
                {/* 反馈详情 */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    {getTypeBadge(selectedFeedback.feedback_type)}
                    {getStatusBadge(selectedFeedback.status)}
                    {selectedFeedback.rating && renderRating(selectedFeedback.rating)}
                  </div>
                  <h3 className="font-medium mb-2">{selectedFeedback.title || "No title"}</h3>
                  <p className="text-gray-700 mb-2">{selectedFeedback.content}</p>
                  <div className="text-sm text-gray-500">
                    From: {selectedFeedback.user_email || "Anonymous"} • 
                    {moment(selectedFeedback.created_at).format("YYYY-MM-DD HH:mm:ss")}
                  </div>
                </div>

                {/* 状态更新 */}
                <div>
                  <label className="block text-sm font-medium mb-2">Update Status</label>
                  <Select value={newStatus} onValueChange={setNewStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 管理员回复 */}
                <div>
                  <label className="block text-sm font-medium mb-2">Admin Reply (Optional)</label>
                  <Textarea
                    value={adminReply}
                    onChange={(e) => setAdminReply(e.target.value)}
                    placeholder="Enter your reply to the user..."
                    rows={4}
                    maxLength={2000}
                  />
                  <div className="text-sm text-gray-500 mt-1">
                    {adminReply.length}/2000 characters
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-end gap-2">
                  <Button
                    onClick={() => setReplyDialogOpen(false)}
                    variant="outline"
                    disabled={updating}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleUpdateFeedback}
                    disabled={updating || !newStatus}
                  >
                    {updating ? "Updating..." : "Update Feedback"}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
