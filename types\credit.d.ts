export interface Credit {
  trans_no: string;
  created_at: string;
  user_uuid: string;
  trans_type: string;
  credits: number;
  order_no: string;
  expired_at?: string;
}

// Image generation settings interface
export interface GenerationSettings {
  imageCount: number;
  highQuality: boolean;
}

// Credit calculation result interface
export interface CreditCalculation {
  totalCredits: number;
  baseCredits: number;
  qualityMultiplier: number;
  imageCount: number;
}

// Credit check result interface
export interface CreditCheckResult {
  sufficient: boolean;
  currentCredits: number;
  requiredCredits: number;
}

// Admin gift credits interface
export interface AdminGiftCredits {
  userEmail: string;
  transType: string;
  credits: number;
  expiredAt: string;
  adminEmail: string;
}
