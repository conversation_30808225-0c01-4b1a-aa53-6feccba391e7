"use client";

import { Check, Loader } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import Icon from "@/components/icon";
import { Label } from "@/components/ui/label";
import { loadStripe } from "@stripe/stripe-js";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { useTranslations } from "next-intl";

export default function Pricing({ pricing }: { pricing: PricingType }) {
  if (pricing.disabled) {
    return null;
  }

  const t = useTranslations();
  const { user, setShowSignModal } = useAppContext();

  const [group, setGroup] = useState(pricing.groups?.[0]?.name);
  const [isLoading, setIsLoading] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  // 月费/年费切换状态 (true = 年费, false = 月费)
  const [isYearly, setIsYearly] = useState(true);

  // 翻转动画状态
  const [isFlipping, setIsFlipping] = useState(false);

  // 视图切换状态
  const [currentView, setCurrentView] = useState<'pricing' | 'addon'>('pricing');

  // 加量包数据
  const addonItems = [
    {
      title: t("pricing.addon_items.small.title"),
      description: t("pricing.addon_items.small.description"),
      price: "29",
      original_price: "39",
      currency: "USD",
      amount: 29,
      cn_amount: 199,
      credits: 50,
      valid_months: 1,
      interval: "month",
      product_id: "addon_small",
      features: [
        t("pricing.addon_items.small.feature_0"),
        t("pricing.addon_items.small.feature_1"),
        t("pricing.addon_items.small.feature_2"),
        t("pricing.addon_items.small.feature_3")
      ]
    },
    {
      title: t("pricing.addon_items.medium.title"),
      description: t("pricing.addon_items.medium.description"),
      price: "59",
      original_price: "79",
      currency: "USD",
      amount: 59,
      cn_amount: 399,
      credits: 120,
      valid_months: 2,
      interval: "month",
      product_id: "addon_medium",
      features: [
        t("pricing.addon_items.medium.feature_0"),
        t("pricing.addon_items.medium.feature_1"),
        t("pricing.addon_items.medium.feature_2"),
        t("pricing.addon_items.medium.feature_3"),
        t("pricing.addon_items.medium.feature_4")
      ],
      popular: true
    },
    {
      title: t("pricing.addon_items.large.title"),
      description: t("pricing.addon_items.large.description"),
      price: "99",
      original_price: "129",
      currency: "USD",
      amount: 99,
      cn_amount: 699,
      credits: 250,
      valid_months: 3,
      interval: "month",
      product_id: "addon_large",
      features: [
        t("pricing.addon_items.large.feature_0"),
        t("pricing.addon_items.large.feature_1"),
        t("pricing.addon_items.large.feature_2"),
        t("pricing.addon_items.large.feature_3"),
        t("pricing.addon_items.large.feature_4"),
        t("pricing.addon_items.large.feature_5")
      ]
    }
  ];

  // 处理 Switch 切换，触发翻转动画
  const handleYearlyToggle = (checked: boolean) => {
    setIsFlipping(true);

    // 在翻转到一半时更新数据
    setTimeout(() => {
      setIsYearly(checked);
    }, 150); // 动画时长的一半

    // 动画结束后重置状态
    setTimeout(() => {
      setIsFlipping(false);
    }, 300);
  };

  // 视图切换处理函数
  const handleViewToggle = () => {
    setCurrentView(currentView === 'pricing' ? 'addon' : 'pricing');
  };

  // 获取加量包价格数据（根据月费/年费状态）
  const getCurrentAddonPriceData = (item: any) => {
    if (isYearly) {
      // 年费数据（当前数据）
      return {
        price: item.price,
        originalPrice: item.original_price,
        amount: item.amount,
        cnAmount: item.cn_amount,
        credits: item.credits,
        validMonths: item.valid_months,
        currency: item.currency,
      };
    } else {
      // 月费数据（年费除以12）
      const yearlyPrice = parseInt(item.price?.replace(/[^0-9]/g, '') || '0');
      const yearlyOriginalPrice = parseInt(item.original_price?.replace(/[^0-9]/g, '') || '0');

      return {
        price: `${Math.round(yearlyPrice / 12)}`,
        originalPrice: yearlyOriginalPrice > 0 ? `${Math.round(yearlyOriginalPrice / 12)}` : undefined,
        amount: Math.round(item.amount / 12),
        cnAmount: item.cn_amount ? Math.round(item.cn_amount / 12) : undefined,
        credits: Math.round((item.credits || 0) / 12),
        validMonths: 1,
        currency: item.currency,
      };
    }
  };

  // 处理加量包购买
  const handleAddonCheckout = async (item: any) => {
    const currentPriceData = getCurrentAddonPriceData(item);

    setIsLoading(true);
    setProductId(item.product_id);

    try {
      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          priceId: item.product_id,
          amount: currentPriceData.amount,
          credits: currentPriceData.credits,
          validMonths: currentPriceData.validMonths,
          isYearly: isYearly,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }

      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      console.error("Error creating checkout session:", error);
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  // 获取当前价格数据（根据月费/年费状态）
  const getCurrentPriceData = (item: PricingItem) => {
    if (isYearly) {
      // 年费数据（当前数据）
      return {
        price: item.price,
        originalPrice: item.original_price,
        amount: item.amount,
        cnAmount: item.cn_amount,
        credits: item.credits,
        validMonths: item.valid_months,
      };
    } else {
      // 月费数据（价格除以12，积分除以12，有效期1个月）
      const yearlyPrice = parseInt(item.price?.replace(/[^0-9]/g, '') || '0');
      const yearlyOriginalPrice = parseInt(item.original_price?.replace(/[^0-9]/g, '') || '0');

      return {
        price: `$${Math.round(yearlyPrice / 12)}`,
        originalPrice: yearlyOriginalPrice > 0 ? `$${Math.round(yearlyOriginalPrice / 12)}` : undefined,
        amount: Math.round(item.amount / 12),
        cnAmount: item.cn_amount ? Math.round(item.cn_amount / 12) : undefined,
        credits: Math.round((item.credits || 0) / 12),
        validMonths: 1,
      };
    }
  };

  const handleCheckout = async (item: PricingItem, cn_pay: boolean = false) => {
    try {
      if (!user) {
        setShowSignModal(true);
        return;
      }

      const currentPriceData = getCurrentPriceData(item);
      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: currentPriceData.credits,
        interval: item.interval,
        amount: cn_pay ? (currentPriceData.cnAmount || currentPriceData.amount) : currentPriceData.amount,
        currency: cn_pay ? "cny" : item.currency,
        valid_months: currentPriceData.validMonths,
      };

      setIsLoading(true);
      setProductId(item.product_id);

      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setProductId(null);

        setShowSignModal(true);
        return;
      }

      const { code, message, data } = await response.json();
      if (code !== 0) {
        toast.error(message);
        return;
      }

      const { public_key, session_id } = data;

      const stripe = await loadStripe(public_key);
      if (!stripe) {
        toast.error("checkout failed");
        return;
      }

      const result = await stripe.redirectToCheckout({
        sessionId: session_id,
      });

      if (result.error) {
        toast.error(result.error.message);
      }
    } catch (e) {
      console.log("checkout failed: ", e);

      toast.error("checkout failed");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  useEffect(() => {
    if (pricing.items) {
      setGroup(pricing.items[0].group);
      setProductId(pricing.items[0].product_id);
      setIsLoading(false);
    }
  }, [pricing.items]);

  return (
    <section id={pricing.name} className="py-32">
      <div className="container">
        <div className="p-8">
          <div className="mx-auto mb-12 text-center">
            <h2 className="mb-4 text-4xl font-semibold lg:text-5xl text-white drop-shadow-lg">
              {pricing.title}
            </h2>
            <p className="text-brand-dark lg:text-lg">
              {pricing.description}
            </p>
          </div>
          <div className="flex flex-col items-center gap-2">
            {pricing.groups && pricing.groups.length > 0 && (
              <div className="flex h-12 mb-12 items-center rounded-md bg-white/10 p-1 text-lg">
                <RadioGroup
                  value={group}
                  className={`h-full grid-cols-${pricing.groups.length}`}
                  onValueChange={(value) => {
                    setGroup(value);
                  }}
                >
                  {pricing.groups.map((item, i) => {
                    return (
                      <div
                        key={i}
                        className='h-full rounded-md transition-all has-[button[data-state="checked"]]:bg-white'
                      >
                        <RadioGroupItem
                          value={item.name || ""}
                          id={item.name}
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor={item.name}
                          className="flex h-full cursor-pointer items-center justify-center px-7 font-semibold text-white peer-data-[state=checked]:text-primary"
                        >
                          {item.title}
                          {item.label && (
                            <Badge
                              variant="outline"
                              className="border-primary bg-primary px-1.5 ml-1 text-primary-foreground"
                            >
                              {item.label}
                            </Badge>
                          )}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>
            )}

            {/* 动画容器 - 包含Switch和卡片 */}
            <div className="mt-8 relative overflow-hidden">
            <div
              className={`flex transition-transform duration-300 ease-in-out will-change-transform ${
                currentView === "pricing" ? "translate-x-0" : "-translate-x-1/2"
              }`}
              style={{
                width: "200%",
                backfaceVisibility: "hidden",
                transform: currentView === "pricing" ? "translateX(0)" : "translateX(-50%)"
              }}
            >
              {/* 价格卡片视图 */}
              <div className="w-1/2 flex-shrink-0">
                {/* 月费/年费切换器 - 价格视图 */}
                <div className="flex items-center justify-center mb-8">
                  <div className="relative">
                    {/* 二重模糊度背景效果 */}
                    <div className="absolute inset-0 bg-black/10 backdrop-blur-sm rounded-full"></div>
                    <div className="absolute inset-0 bg-black/10 backdrop-blur-sm rounded-full"></div>
                    <div className="relative flex items-center gap-4 rounded-full p-2 border border-white/20 shadow-lg">
                      <span className={`px-4 py-2 text-sm font-semibold transition-colors duration-300 ${!isYearly ? 'text-white drop-shadow-sm' : 'text-white/70'}`}>
                        {t("pricing.monthly")}
                      </span>
                      <div className="relative">
                        {/* Switch 二重模糊度背景 */}
                        <div className="absolute inset-0 bg-black/10 backdrop-blur-sm rounded-full"></div>
                        <div className="absolute inset-0 bg-black/10 backdrop-blur-sm rounded-full"></div>
                        <Switch
                          checked={isYearly}
                          onCheckedChange={handleYearlyToggle}
                          className="relative data-[state=checked]:bg-primary data-[state=unchecked]:bg-transparent border-white/20"
                        />
                      </div>
                      <span className={`px-4 py-2 text-sm font-semibold transition-colors duration-300 ${isYearly ? 'text-white drop-shadow-sm' : 'text-white/70'}`}>
                        {t("pricing.yearly")}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
            {pricing.items?.map((item, index) => {
              if (item.group && item.group !== group) {
                return null;
              }

              // 计算当前项在过滤后数组中的位置
              const filteredItems = pricing.items?.filter(
                (filterItem) => !filterItem.group || filterItem.group === group
              ) || [];
              const currentIndex = filteredItems.findIndex(filterItem => filterItem === item);
              const totalItems = filteredItems.length;
              const isMiddleItem = totalItems === 3 && currentIndex === 1; // 中间的卡片

              // 获取当前价格数据
              const currentPriceData = getCurrentPriceData(item);

              return (
                <div
                  key={index}
                  className={`rounded-lg p-6 transition-transform duration-300 ease-in-out h-[712px] ${
                    isFlipping ? 'scale-x-0' : 'scale-x-100'
                  } ${
                    item.is_featured
                      ? `border-white/30 border-2 ${isMiddleItem ? 'bg-black/60 backdrop-blur-md' : 'bg-black/40 backdrop-blur-sm'} text-white`
                      : `border-white/20 border ${isMiddleItem ? 'bg-black/50 backdrop-blur-md' : 'bg-black/35 backdrop-blur-sm'} text-white`
                  }`}
                  style={{
                    transformStyle: 'preserve-3d',
                    backfaceVisibility: 'hidden',
                  }}
                >
                  <div className="flex h-full flex-col justify-between gap-5">
                    <div>
                      <div className="flex items-center gap-2 mb-4">
                        {item.title && (
                          <h3 className="text-xl font-semibold text-white">
                            {item.title}
                          </h3>
                        )}
                        <div className="flex-1"></div>
                        {item.label && (
                          <Badge
                            variant="outline"
                            className="border-primary bg-primary px-1.5 text-primary-foreground"
                          >
                            {item.label}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-end gap-2 mb-4">
                        {currentPriceData.originalPrice && (
                          <span className="text-xl text-white/60 font-semibold line-through">
                            {currentPriceData.originalPrice}
                          </span>
                        )}
                        {currentPriceData.price && (
                          <span className="text-5xl font-semibold text-white">
                            {currentPriceData.price}
                          </span>
                        )}
                        {item.unit && (
                          <span className="block font-semibold text-white">
                            {item.unit}
                          </span>
                        )}
                      </div>
                      {item.description && (
                        <p className="text-brand-light">
                          {item.description}
                        </p>
                      )}
                      {item.features_title && (
                        <p className="mb-3 mt-6 font-semibold text-brand-light">
                          {item.features_title}
                        </p>
                      )}
                      {item.features && (
                        <ul className="flex flex-col gap-3">
                          {item.features.map((feature, fi) => {
                            // 动态更新第一个 feature（积分信息）
                            let displayFeature = feature;
                            if (fi === 0 && feature.includes('credits')) {
                              displayFeature = t("pricing.credits_validity", {
                                credits: currentPriceData.credits,
                                months: currentPriceData.validMonths,
                                plural: (currentPriceData.validMonths || 1) > 1 ? 's' : ''
                              });
                            }

                            return (
                              <li className="flex gap-2 text-brand-light" key={`feature-${fi}`}>
                                <Check className="mt-1 size-4 shrink-0 text-brand-light" />
                                {displayFeature}
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      {currentPriceData.cnAmount && currentPriceData.cnAmount > 0 ? (
                        <div className="flex items-center gap-x-2 mt-2">
                          <span className="text-sm text-brand-light">{t("pricing.cny_payment")}</span>
                          <div
                            className="inline-block p-2 hover:cursor-pointer hover:bg-white/10 rounded-md"
                            onClick={() => {
                              if (isLoading) {
                                return;
                              }
                              handleCheckout(item, true);
                            }}
                          >
                            <img
                              src="/imgs/cnpay.png"
                              alt="cnpay"
                              className="w-20 h-10 rounded-lg"
                            />
                          </div>
                        </div>
                      ) : null}
                      {item.button && (
                        <Button
                          className="w-full flex items-center justify-center gap-2 font-semibold"
                          disabled={isLoading}
                          onClick={() => {
                            if (isLoading) {
                              return;
                            }
                            handleCheckout(item);
                          }}
                        >
                          {(!isLoading ||
                            (isLoading && productId !== item.product_id)) && (
                            <p>{item.button.title}</p>
                          )}

                          {isLoading && productId === item.product_id && (
                            <p>{item.button.title}</p>
                          )}
                          {isLoading && productId === item.product_id && (
                            <Loader className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          {item.button.icon && (
                            <Icon name={item.button.icon} className="size-4" />
                          )}
                        </Button>
                      )}
                      {item.tip && (
                        <p className="text-white/60 text-sm mt-2">
                          {item.tip}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
                </div>
              </div>

              {/* 加量包视图 */}
              <div className="w-1/2 flex-shrink-0">
                {/* 占位空间，与月费切换器高度一致 */}
                <div className="mb-8 h-[55px]"></div>

                <div className="grid gap-6 md:grid-cols-3">
                {addonItems.map((item, index) => {
                  const isMiddleItem = index === 1; // 中间的卡片

                  // 获取当前价格数据
                  const currentPriceData = getCurrentAddonPriceData(item);

                  return (
                    <div
                      key={item.product_id}
                      className={`rounded-lg p-6 transition-transform duration-300 ease-in-out h-[712px] ${
                        isFlipping ? 'scale-x-0' : 'scale-x-100'
                      } ${
                        item.popular
                          ? `border-white/30 border-2 ${isMiddleItem ? 'bg-black/60 backdrop-blur-md' : 'bg-black/40 backdrop-blur-sm'} text-white`
                          : `border-white/20 border ${isMiddleItem ? 'bg-black/50 backdrop-blur-md' : 'bg-black/35 backdrop-blur-sm'} text-white`
                      }`}
                      style={{
                        transformStyle: 'preserve-3d',
                        backfaceVisibility: 'hidden',
                      }}
                    >
                      <div className="flex h-full flex-col justify-between gap-5">
                        <div>
                          <div className="flex items-center gap-2 mb-4">
                            {item.title && (
                              <h3 className="text-xl font-semibold text-white">
                                {item.title}
                              </h3>
                            )}
                            <div className="flex-1"></div>
                            {item.popular && (
                              <Badge
                                variant="outline"
                                className="border-primary bg-primary px-1.5 text-primary-foreground"
                              >
                                {t("pricing.popular")}
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-end gap-2 mb-4">
                            {currentPriceData.originalPrice && (
                              <span className="text-xl text-white/60 font-semibold line-through">
                                ${currentPriceData.originalPrice}
                              </span>
                            )}
                            {currentPriceData.price && (
                              <span className="text-5xl font-semibold text-white">
                                ${currentPriceData.price}
                              </span>
                            )}
                            <span className="block font-semibold text-white">
                              {currentPriceData.currency}
                            </span>
                          </div>
                          {item.description && (
                            <p className="text-brand-light">
                              {item.description}
                            </p>
                          )}
                          <p className="mb-3 mt-6 font-semibold text-brand-light">
                            {t("pricing.includes")}
                          </p>
                          <ul className="flex flex-col gap-3">
                            <li className="flex gap-2 text-brand-light">
                              <Check className="mt-1 size-4 shrink-0 text-brand-light" />
                              {t("pricing.credits_validity", {
                                credits: currentPriceData.credits,
                                months: currentPriceData.validMonths,
                                plural: currentPriceData.validMonths > 1 ? 's' : ''
                              })}
                            </li>
                            {item.features?.map((feature, featureIndex) => (
                              <li key={featureIndex} className="flex gap-2 text-brand-light">
                                <Check className="mt-1 size-4 shrink-0 text-brand-light" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div className="flex flex-col gap-2">
                          {currentPriceData.cnAmount && currentPriceData.cnAmount > 0 ? (
                            <div className="flex items-center gap-x-2 mt-2">
                              <span className="text-sm text-brand-light">{t("pricing.cny_payment")}</span>
                              <div
                                className="inline-block p-2 hover:cursor-pointer hover:bg-white/10 rounded-md"
                                onClick={() => {
                                  if (isLoading) {
                                    return;
                                  }
                                  handleAddonCheckout(item);
                                }}
                              >
                                <img
                                  src="/imgs/cnpay.png"
                                  alt="cnpay"
                                  className="w-20 h-10 rounded-lg"
                                />
                              </div>
                            </div>
                          ) : null}
                          <Button
                            className="w-full flex items-center justify-center gap-2 font-semibold"
                            disabled={isLoading}
                            onClick={() => {
                              if (isLoading) {
                                return;
                              }
                              handleAddonCheckout(item);
                            }}
                          >
                            {(!isLoading ||
                              (isLoading && productId !== item.product_id)) && (
                              <p>{t("pricing.purchase_addon")}</p>
                            )}

                            {isLoading && productId === item.product_id && (
                              <p>{t("pricing.purchase_addon")}</p>
                            )}
                            {isLoading && productId === item.product_id && (
                              <Loader className="mr-2 h-4 w-4 animate-spin" />
                            )}
                          </Button>
                          <p className="text-white/60 text-sm mt-2">
                            一次购买，立即生效！
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 加量包/返回按钮 */}
        <div className="flex flex-col items-center mt-12 gap-3">
          {currentView === 'pricing' && (
            <p className="text-brand-dark text-sm font-medium">
              {t("pricing.need_more_credits")}
            </p>
          )}
          {currentView === 'addon' && (
            <p className="text-brand-dark text-sm font-medium">
              {t("pricing.prefer_subscription")}
            </p>
          )}
          <div className="relative">
            <div className="absolute inset-0 bg-black/10 backdrop-blur-sm rounded-lg"></div>
            <div className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-lg"></div>
            <div className="absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg"></div>
            <Button
              onClick={handleViewToggle}
              className="relative px-8 py-3 bg-primary hover:bg-primary/80 text-white border border-white/20 backdrop-blur-sm rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
              variant="outline"
            >
            {currentView === 'pricing' ? (
              <div className="flex items-center gap-2">
                <span>{t("pricing.addon_packages")}</span>
                <Icon name="arrow-right" className="size-4" />
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Icon name="arrow-left" className="size-4" />
                <span>{t("pricing.back_to_pricing")}</span>
              </div>
            )}
          </Button>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
