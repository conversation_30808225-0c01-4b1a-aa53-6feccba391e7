"use client";

import { useState } from "react";
import { ImageData, ImageSlotStatus } from "@/types/image-results";
import { cn } from "@/lib/utils";
import ImageSlotProgress from "./ImageSlotProgress";

interface ImageGridProps {
  images: ImageData[];
  onImageClick: (image: ImageData, index: number) => void;
  className?: string;
  imageSlots?: ImageSlotStatus[]; // Status for each image slot
  translations?: {
    status?: {
      waiting?: string;
      countdown?: string;
      generating?: string;
      completed?: string;
      failed?: string;
      preparing?: string;
    };
    messages?: {
      countdown_seconds?: string;
      slot_number?: string;
      retry?: string;
      generation_error?: string;
      generation_failed?: string;
    };
  };
}

export default function ImageGrid({ images, onImageClick, className, imageSlots, translations }: ImageGridProps) {
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());
  const [errorImages, setErrorImages] = useState<Set<number>>(new Set());

  const handleImageLoad = (index: number) => {
    setLoadedImages(prev => new Set([...prev, index]));
  };

  const handleImageError = (index: number) => {
    setErrorImages(prev => new Set([...prev, index]));
  };

  // 确定实际需要显示图片的位置数量
  const totalSlots = imageSlots ? imageSlots.length : Math.max(images.length, 1);
  const activeSlots = Math.min(totalSlots, 4); // 最多4个位置

  return (
    <div className={cn("grid grid-cols-4 gap-3 w-full", className)}>
      {/* 固定渲染4个位置，保持网格布局一致性 */}
      {Array.from({ length: 4 }, (_, position) => {
        // 只在前activeSlots个位置显示内容
        const shouldShowContent = position < activeSlots;

        if (!shouldShowContent) {
          // 空白位置，不显示任何内容但保持网格结构
          return <div key={position} className="aspect-square" />;
        }

        const image = images[position]; // 获取对应位置的图片
        const slotStatus = imageSlots?.find(slot => slot.index === position);
        const hasImage = image !== undefined;
        const imageLoaded = loadedImages.has(position);
        const imageError = errorImages.has(position);
        const canClickImage = hasImage && imageLoaded && !imageError;
        const showSlotProgress = slotStatus && ['waiting', 'countdown', 'generating'].includes(slotStatus.status);

        // 检查是否是单个图片槽的失败状态
        const isSlotFailed = slotStatus?.status === 'failed';

        return (
          <div
            key={position}
            className={cn(
              "relative overflow-hidden rounded-lg transition-all duration-300 aspect-square",
              canClickImage ? "cursor-pointer group hover:scale-[1.02] hover:shadow-lg" : "",
              imageError ? "cursor-not-allowed" : ""
            )}
            onClick={canClickImage ? () => onImageClick(image, position) : undefined}
          >
            {showSlotProgress ? (
              /* 显示倒计时/生成进度 */
              <ImageSlotProgress slotStatus={slotStatus} translations={translations} />
            ) : isSlotFailed ? (
              /* 单个图片槽生成失败 */
              <div className="w-full h-full bg-gradient-to-br from-red-50 to-red-100 border-2 border-red-200 rounded-lg flex items-center justify-center">
                <div className="text-center text-red-600 p-4">
                  <div className="text-3xl mb-2">⚠️</div>
                  <div className="text-sm font-medium mb-1">{translations?.status?.failed || '生成失败'}</div>
                  <div className="text-xs text-red-500">
                    {slotStatus?.error || (translations?.messages?.generation_error || '图片生成出错')}
                  </div>
                </div>
              </div>
            ) : hasImage ? (
              <>
                {/* Loading placeholder */}
                {!loadedImages.has(position) && !errorImages.has(position) && (
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
                    <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  </div>
                )}

                {/* Error state */}
                {errorImages.has(position) && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-100 to-red-200 flex items-center justify-center">
                    <div className="text-center text-red-600">
                      <div className="text-2xl mb-2">⚠️</div>
                      <div className="text-sm">加载失败</div>
                    </div>
                  </div>
                )}

                {/* Image */}
                <img
                  src={image.url}
                  alt={`Generated image ${position + 1}`}
                  className={cn(
                    "w-full h-full object-contain transition-all duration-500",
                    loadedImages.has(position) ? "opacity-100" : "opacity-0"
                  )}
                  onLoad={() => handleImageLoad(position)}
                  onError={() => handleImageError(position)}
                />

                {/* Error overlay - 图片加载失败时显示 */}
                {imageError && (
                  <div className="absolute inset-0 bg-red-500/20 flex items-center justify-center">
                    <div className="bg-red-500/90 backdrop-blur-sm rounded-lg p-3 text-center">
                      <svg className="w-8 h-8 text-white mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <div className="text-xs text-white font-medium">加载失败</div>
                    </div>
                  </div>
                )}

                {/* Hover overlay - 只有成功加载的图片才显示 */}
                {canClickImage && (
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                        <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                )}

              </>
            ) : null /* 空位置不显示任何内容 */
            }
          </div>
        );
      })}
    </div>
  );
}
