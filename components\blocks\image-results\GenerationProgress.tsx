"use client";

import { useEffect, useState } from "react";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { X, Clock, Zap } from "lucide-react";
import { cn } from "@/lib/utils";

interface GenerationProgressProps {
  isGenerating: boolean;
  countdown?: number;
  onCancel?: () => void;
  enableCountdown?: boolean;
  stage?: 'waiting' | 'countdown' | 'generating';
  queuePosition?: number;
  totalQueue?: number;
}

export default function GenerationProgress({
  isGenerating,
  countdown = 0,
  onCancel,
  enableCountdown = true,
  stage = 'generating',
  queuePosition = 0,
  totalQueue = 0,
}: GenerationProgressProps) {
  const [currentCountdown, setCurrentCountdown] = useState(countdown);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    setCurrentCountdown(countdown);
  }, [countdown]);

  useEffect(() => {
    if (stage === 'countdown' && currentCountdown > 0) {
      const timer = setInterval(() => {
        setCurrentCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [stage, currentCountdown]);

  useEffect(() => {
    if (stage === 'countdown' && countdown > 0) {
      const progressValue = ((countdown - currentCountdown) / countdown) * 100;
      setProgress(progressValue);
    } else if (stage === 'generating') {
      // Simulate generation progress
      const timer = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 5;
        });
      }, 500);

      return () => clearInterval(timer);
    } else {
      setProgress(0);
    }
  }, [stage, countdown, currentCountdown]);

  if (!isGenerating && stage !== 'waiting') return null;

  const getStageInfo = () => {
    switch (stage) {
      case 'waiting':
        return {
          icon: Clock,
          title: '等待中...',
          description: queuePosition > 0 
            ? `队列中第 ${queuePosition} 位，共 ${totalQueue} 个任务`
            : '准备开始生成',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
        };
      case 'countdown':
        return {
          icon: Clock,
          title: `倒计时 ${currentCountdown}s`,
          description: '即将开始生成图片',
          color: 'text-blue-500',
          bgColor: 'bg-blue-500/10',
        };
      case 'generating':
        return {
          icon: Zap,
          title: '正在生成...',
          description: 'AI正在创作您的图片，请稍候',
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
        };
      default:
        return {
          icon: Zap,
          title: '处理中...',
          description: '请稍候',
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/10',
        };
    }
  };

  const stageInfo = getStageInfo();
  const IconComponent = stageInfo.icon;

  return (
    <div className="w-full max-w-md mx-auto">
      <div className={cn(
        "relative overflow-hidden rounded-2xl border border-white/20 backdrop-blur-md p-6",
        stageInfo.bgColor
      )}>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={cn("p-2 rounded-full", stageInfo.bgColor)}>
              <IconComponent className={cn("w-5 h-5", stageInfo.color)} />
            </div>
            <div>
              <h3 className="text-white font-medium">{stageInfo.title}</h3>
              <p className="text-white/70 text-sm">{stageInfo.description}</p>
            </div>
          </div>
          
          {onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="text-white/70 hover:text-white hover:bg-white/10"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Progress bar */}
        <div className="space-y-2">
          <Progress 
            value={progress} 
            className="h-2 bg-white/20"
          />
          <div className="flex justify-between text-xs text-white/60">
            <span>
              {stage === 'countdown' ? '等待开始' : 
               stage === 'generating' ? '生成中' : '准备中'}
            </span>
            <span>{Math.round(progress)}%</span>
          </div>
        </div>

        {/* Countdown display */}
        {stage === 'countdown' && enableCountdown && (
          <div className="mt-4 text-center">
            <div className="text-2xl font-bold text-white mb-1">
              {currentCountdown}
            </div>
            <div className="text-white/60 text-sm">
              秒后开始生成
            </div>
          </div>
        )}

        {/* Generation animation */}
        {stage === 'generating' && (
          <div className="mt-4 flex justify-center">
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "w-2 h-2 rounded-full animate-pulse",
                    stageInfo.color.replace('text-', 'bg-')
                  )}
                  style={{
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: '1s',
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Background animation */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12 animate-pulse" />
        </div>
      </div>
    </div>
  );
}
