@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";

html {
  scroll-behavior: smooth;
  /* 防止下拉菜单打开时滚动条隐藏导致的偏移 */
  scrollbar-gutter: stable;
}

/* 确保背景视频覆盖整个视口包括滚动条 */
body {
  overflow-x: hidden;
}

/* 为页面根元素设置背景图片，避免视频加载时的白色闪烁 */
html {
  background: url('/imgs/background/background-poster.webp') center center / cover no-repeat fixed;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* 对于Firefox */
html {
  scrollbar-width: none;
}

/* 对于IE和Edge */
body {
  -ms-overflow-style: none;
}

/* 动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Testimonial 滚动动画 - 不同速度 */
@keyframes testimonialScrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

/* 基础列滚动样式 */
.testimonial-column {
  display: flex;
  flex-direction: column;
  animation: testimonialScrollUp linear infinite;
  will-change: transform;
}

.testimonial-column.paused {
  animation-play-state: paused;
}

/* 不同列的滚动速度 */
.testimonial-column-speed-1 {
  animation-duration: 40s; /* 第一列：40秒 */
}

.testimonial-column-speed-2 {
  animation-duration: 35s; /* 第二列：35秒（更快） */
}

.testimonial-column-speed-3 {
  animation-duration: 45s; /* 第三列：45秒（更慢） */
}

/* 卡片基础样式 - 依靠容器mask实现渐显渐隐 */
.testimonial-card {
  opacity: 1;
  transition: transform 0.3s ease;
}



@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}



/* 备选方案：自定义滚动条样式（如需要可取消注释）
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}


*/

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    background: transparent;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }

}

@layer components {
  /* 主页组件背景样式 - 除了 AIGenerator 组件外的其他组件 */
  .homepage-section-bg {
    @apply bg-black/35 backdrop-blur-sm;
  }

  /* 品牌颜色 - 无遮罩背景的文字与图标色 */
  .text-brand-dark {
    color: #475569;
  }

  /* 品牌颜色 - 有遮罩背景的文字与图标色 */
  .text-brand-light {
    color: #CBD5E1;
  }
}
