"use client";

import { useEffect, useState, useCallback } from "react";
import { Coins, AlertCircle, RefreshCw } from "lucide-react";
import { useAppContext } from "@/contexts/app";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

interface UserCredits {
  left_credits: number;
  is_pro: boolean;
}

export default function CreditsDisplay() {
  const { user } = useAppContext();
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<boolean>(false);
  const t = useTranslations("credits.display");
  const tErrors = useTranslations("credits.errors");

  const fetchCredits = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(false);

      const response = await fetch('/api/user/credits');

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.code === 0 && result.data?.credits) {
        setCredits(result.data.credits);
      } else {
        throw new Error(result.message || 'Failed to fetch credits');
      }
    } catch (error) {
      console.error('Failed to fetch credits:', error);
      setError(true);
      toast.error(tErrors("fetch_failed") || "获取积分信息失败，请重试");
    } finally {
      setLoading(false);
    }
  }, [user, tErrors]);

  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);

  // 添加定期刷新，但频率较低（每30秒）
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      // 只有在页面可见时才刷新
      if (!document.hidden) {
        fetchCredits();
      }
    }, 30000); // 30秒刷新一次

    return () => clearInterval(interval);
  }, [user, fetchCredits]);

  // 页面重新获得焦点时刷新积分
  useEffect(() => {
    if (!user) return;

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchCredits();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user, fetchCredits]);

  // 如果用户未登录，不显示积分
  if (!user) {
    return null;
  }

  // 错误状态显示
  if (error && !loading) {
    return (
      <div
        className="flex items-center gap-2 px-3 py-1 bg-red-500/10 rounded-full border border-red-500/20 cursor-pointer hover:bg-red-500/20 transition-colors"
        onClick={fetchCredits}
        title="点击重试获取积分信息"
      >
        <AlertCircle className="w-4 h-4 text-red-500" />
        <span className="text-sm font-medium text-red-400">
          {tErrors("fetch_failed")}
        </span>
        <RefreshCw className="w-3 h-3 text-red-400" />
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 px-3 py-1 bg-orange-500/10 rounded-full border border-orange-500/20">
      <Coins className="w-4 h-4 text-orange-500" />
      <span className="text-sm font-medium text-white">
        {loading ? t("loading") : credits?.left_credits || 0}
      </span>
    </div>
  );
}
