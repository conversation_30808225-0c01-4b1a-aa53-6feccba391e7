"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { Header as HeaderType } from "@/types/blocks/header";
import Icon from "@/components/icon";
import Link from "next/link";
import LocaleToggle from "@/components/locale/toggle";
import { Menu } from "lucide-react";
import SignToggle from "@/components/sign/toggle";
import { useAppContext } from "@/contexts/app";
import { signOut } from "next-auth/react";
import { useTranslations } from "next-intl";
import CreditsDisplay from "@/components/credits/display";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

export default function Header({ header }: { header: HeaderType }) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (header.disabled) {
    return null;
  }

  return (
    <section className="fixed top-0 left-0 right-0 z-50 py-3 border-b border-white/10 transition-all duration-300">
      {/* 二重模糊度背景效果 - 仅在滚动时显示 */}
      {isScrolled && (
        <>
          <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
        </>
      )}
      <div className="md:max-w-7xl mx-auto px-4 relative">
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            <a
              href={header.brand?.url || ""}
              className="flex items-center gap-2"
            >
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className="w-8"
                />
              )}
              {header.brand?.title && (
                <span className="text-xl font-bold drop-shadow-lg" style={{color: '#FF7A00'}}>
                  {header.brand?.title || ""}
                </span>
              )}
            </a>
            <div className="flex items-center">
              <NavigationMenu>
                <NavigationMenuList>
                  {header.nav?.items?.map((item, i) => {
                    if (item.children && item.children.length > 0) {
                      return (
                        <NavigationMenuItem
                          key={i}
                          className="text-white hover:text-orange-500"
                        >
                          <NavigationMenuTrigger>
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0 mr-2"
                              />
                            )}
                            <span>{item.title}</span>
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            <ul className="w-80 p-3">
                              <NavigationMenuLink>
                                {item.children.map((iitem, ii) => (
                                  <li key={ii}>
                                    <a
                                      className={cn(
                                        "flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                      )}
                                      href={iitem.url}
                                      target={iitem.target}
                                    >
                                      {iitem.icon && (
                                        <Icon
                                          name={iitem.icon}
                                          className="size-5 shrink-0"
                                        />
                                      )}
                                      <div>
                                        <div className="text-sm font-semibold text-white">
                                          {iitem.title}
                                        </div>
                                        <p className="text-sm leading-snug text-white">
                                          {iitem.description}
                                        </p>
                                      </div>
                                    </a>
                                  </li>
                                ))}
                              </NavigationMenuLink>
                            </ul>
                          </NavigationMenuContent>
                        </NavigationMenuItem>
                      );
                    }

                    return (
                      <NavigationMenuItem key={i}>
                        <a
                          className={cn(
                            "text-white hover:text-orange-500",
                            navigationMenuTriggerStyle,
                            buttonVariants({
                              variant: "ghost",
                            })
                          )}
                          href={item.url}
                          target={item.target}
                        >
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0 mr-0"
                            />
                          )}
                          {item.title}
                        </a>
                      </NavigationMenuItem>
                    );
                  })}
                </NavigationMenuList>
              </NavigationMenu>
            </div>
          </div>
          <div className="shrink-0 flex gap-2 items-center">
            {header.show_locale && <LocaleToggle />}

            {header.buttons?.map((item, i) => {
              return (
                <Button key={i} variant={item.variant}>
                  <Link
                    href={item.url || ""}
                    target={item.target || ""}
                    className="flex items-center gap-1"
                  >
                    {item.title}
                    {item.icon && (
                      <Icon name={item.icon} className="size-4 shrink-0" />
                    )}
                  </Link>
                </Button>
              );
            })}
            <CreditsDisplay />
            {header.show_sign && <SignToggle />}
          </div>
        </nav>

        <div className="block lg:hidden">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className="w-8"
                />
              )}
              {header.brand?.title && (
                <span className="text-xl font-bold drop-shadow-lg" style={{color: '#FF7A00'}}>
                  {header.brand?.title || ""}
                </span>
              )}
            </div>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="default" size="icon">
                  <Menu className="size-2" />
                </Button>
              </SheetTrigger>
              <SheetContent className="overflow-y-auto bg-black/20 backdrop-blur-md border-l-2 border-orange-500/30 text-white [&>button]:text-white [&>button]:hover:text-orange-400 [&>button]:hover:bg-orange-500/20">
                <SheetHeader>
                  <SheetTitle>
                    <div className="flex items-center gap-2">
                      {header.brand?.logo?.src && (
                        <img
                          src={header.brand.logo.src}
                          alt={header.brand.logo.alt || header.brand.title}
                          className="w-8"
                        />
                      )}
                      {header.brand?.title && (
                        <span className="text-xl font-bold drop-shadow-lg" style={{color: '#FF7A00'}}>
                          {header.brand?.title || ""}
                        </span>
                      )}
                    </div>
                  </SheetTitle>
                </SheetHeader>
                <div className="mb-8 mt-8 flex flex-col gap-2">
                  {/* Home 链接 */}
                  <a
                    href="/"
                    className="font-semibold flex items-center gap-2 text-white hover:text-orange-400 transition-colors duration-200 hover:bg-orange-500/10 rounded-md px-2 py-2"
                  >
                    <Icon name="HiOutlineHome" className="size-4 shrink-0 text-orange-400" />
                    Home
                  </a>

                  <Accordion type="single" collapsible className="w-full">
                    {header.nav?.items?.map((item, i) => {
                      if (item.children && item.children.length > 0) {
                        return (
                          <AccordionItem
                            key={i}
                            value={item.title || ""}
                            className="border-b-0"
                          >
                            <AccordionTrigger className="mb-4 py-0 font-semibold hover:no-underline text-left text-white hover:text-orange-400 transition-colors">
                              {item.title}
                            </AccordionTrigger>
                            <AccordionContent className="mt-2">
                              {item.children.map((iitem, ii) => (
                                <a
                                  key={ii}
                                  className={cn(
                                    "flex select-none gap-4 rounded-md p-3 leading-none outline-none transition-colors hover:bg-orange-500/20 hover:text-orange-300 focus:bg-orange-500/20 focus:text-orange-300"
                                  )}
                                  href={iitem.url}
                                  target={iitem.target}
                                >
                                  {iitem.icon && (
                                    <Icon
                                      name={iitem.icon}
                                      className="size-4 shrink-0 text-orange-400"
                                    />
                                  )}
                                  <div>
                                    <div className="text-sm font-semibold text-white">
                                      {iitem.title}
                                    </div>
                                    <p className="text-sm leading-snug text-gray-300">
                                      {iitem.description}
                                    </p>
                                  </div>
                                </a>
                              ))}
                            </AccordionContent>
                          </AccordionItem>
                        );
                      }
                      return (
                        <a
                          key={i}
                          href={item.url}
                          target={item.target}
                          className="font-semibold flex items-center gap-2 text-white hover:text-orange-400 transition-colors duration-200 hover:bg-orange-500/10 rounded-md px-2 py-2"
                        >
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0 text-orange-400"
                            />
                          )}
                          {item.title}
                        </a>
                      );
                    })}
                  </Accordion>
                </div>
                <div className="flex-1"></div>
                <div className="border-t border-orange-500/30 pt-4">
                  <div className="mt-2 flex flex-col gap-3">
                    {header.buttons?.map((item, i) => {
                      return (
                        <Button key={i} variant={item.variant} className="bg-orange-600 hover:bg-orange-700 text-white border-none">
                          <Link
                            href={item.url || ""}
                            target={item.target || ""}
                            className="flex items-center gap-1"
                          >
                            {item.title}
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0"
                              />
                            )}
                          </Link>
                        </Button>
                      );
                    })}

                    <CreditsDisplay />
                    {header.show_sign && <SidebarUserMenu />}

                    {/* 语言切换器移到最下方 */}
                    {header.show_locale && (
                      <div className="mt-2 flex items-center justify-center">
                        <LocaleToggle />
                      </div>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </section>
  );
}

// 侧边栏专用的用户菜单组件
function SidebarUserMenu() {
  const t = useTranslations();
  const { user } = useAppContext();

  if (!user) {
    return <SignToggle />;
  }

  return (
    <div className="flex flex-col gap-2">
      {/* 用户头像和信息 */}
      <div className="flex items-center gap-2 px-2 py-2 rounded-md bg-orange-500/10">
        <div className="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full">
          <img className="aspect-square h-full w-full" alt={user.nickname} src={user.avatar_url} />
        </div>
        <div className="flex flex-col">
          <span className="text-sm font-medium text-white truncate">{user.nickname}</span>
          <span className="text-xs text-gray-300 truncate">{user.email}</span>
        </div>
      </div>

      {/* 菜单项 */}
      <div className="flex flex-col gap-1">
        <Link
          href="/my-orders"
          className="flex items-center gap-2 px-2 py-2 text-sm text-white hover:text-orange-400 hover:bg-orange-500/10 rounded-md transition-colors"
        >
          <Icon name="RiShoppingBag3Line" className="size-4 text-orange-400" />
          {t("user.my_orders")}
        </Link>

        <Link
          href="/my-credits"
          className="flex items-center gap-2 px-2 py-2 text-sm text-white hover:text-orange-400 hover:bg-orange-500/10 rounded-md transition-colors"
        >
          <Icon name="RiCoinLine" className="size-4 text-orange-400" />
          {t("my_credits.title")}
        </Link>

        <button
          onClick={() => signOut()}
          className="flex items-center gap-2 px-2 py-2 text-sm text-white hover:text-orange-400 hover:bg-orange-500/10 rounded-md transition-colors text-left"
        >
          <Icon name="RiLogoutBoxLine" className="size-4 text-orange-400" />
          {t("user.sign_out")}
        </button>
      </div>
    </div>
  );
}
