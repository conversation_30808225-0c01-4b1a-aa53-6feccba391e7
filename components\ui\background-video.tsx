"use client";

import { useEffect, useRef, useState } from "react";

interface BackgroundVideoProps {
  videoSrc: string;
  className?: string;
}

export function BackgroundVideo({
  videoSrc,
  className = ""
}: BackgroundVideoProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [showVideo, setShowVideo] = useState(false);

  useEffect(() => {
    // 等待页面内容完全加载完毕后再加载视频
    const handleLoad = () => {
      // 额外延迟一点时间，确保页面渲染完成
      setTimeout(() => {
        setShowVideo(true);
      }, 500);
    };

    // 检查页面是否已经加载完成
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      // 监听页面加载完成事件
      window.addEventListener('load', handleLoad);

      return () => {
        window.removeEventListener('load', handleLoad);
      };
    }
  }, []);

  return (
    <div
      className={`fixed inset-0 w-screen h-screen overflow-hidden pointer-events-none z-[-1] ${className}`}
      style={{ width: '100vw', height: '100vh' }}
    >
      {/* 背景视频 */}
      {showVideo && (
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-1000"
          muted
          loop
          playsInline
          autoPlay
          preload="auto"
          onCanPlay={() => {
            if (videoRef.current) {
              videoRef.current.style.opacity = '1';
            }
          }}
          onError={(e) => {
            console.log("视频加载失败:", e);
          }}
        >
          <source src={videoSrc} type="video/webm" />
        </video>
      )}


    </div>
  );
}

export default BackgroundVideo;
