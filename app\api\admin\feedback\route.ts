import { NextRequest } from "next/server";
import { getUserInfo } from "@/services/user";
import { getFeedbacks, getFeedbackStats } from "@/models/feedback";
import { updateFeedbackStatus, validateFeedbackStatus } from "@/services/feedback";
import { respData, respErr } from "@/lib/resp";

/**
 * GET /api/admin/feedback
 * Get all feedback for admin management
 */
export async function GET(req: NextRequest) {
  try {
    // Verify admin permissions
    const userInfo = await getUserInfo();
    if (!userInfo || !userInfo.email) {
      return respErr("Unauthorized", 401);
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return respErr("Access denied", 403);
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const status = searchParams.get("status") || undefined;
    const feedback_type = searchParams.get("feedback_type") || undefined;
    const stats = searchParams.get("stats") === "true";

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return respErr("Invalid pagination parameters", 400);
    }

    // Get feedback list
    const feedbacks = await getFeedbacks(page, limit, status, feedback_type);

    // Get stats if requested
    let feedbackStats = null;
    if (stats) {
      feedbackStats = await getFeedbackStats();
    }

    return respData({
      feedbacks,
      page,
      limit,
      total: feedbacks.length,
      stats: feedbackStats,
      filters: {
        status,
        feedback_type,
      },
    });

  } catch (error) {
    console.error("Get admin feedback error:", error);
    return respErr("Failed to get feedback", 500);
  }
}

/**
 * PUT /api/admin/feedback
 * Update feedback status and admin reply
 */
export async function PUT(req: NextRequest) {
  try {
    // Verify admin permissions
    const userInfo = await getUserInfo();
    if (!userInfo || !userInfo.email) {
      return respErr("Unauthorized", 401);
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return respErr("Access denied", 403);
    }

    // Parse request body
    const { uuid, status, admin_reply } = await req.json();

    // Validate required fields
    if (!uuid || typeof uuid !== 'string') {
      return respErr("Feedback UUID is required", 400);
    }

    if (!status || typeof status !== 'string') {
      return respErr("Status is required", 400);
    }

    // Validate status
    if (!validateFeedbackStatus(status)) {
      return respErr("Invalid status", 400);
    }

    // Validate admin reply length if provided
    if (admin_reply && admin_reply.length > 2000) {
      return respErr("Admin reply is too long (max 2000 characters)", 400);
    }

    // Update feedback
    await updateFeedbackStatus({
      uuid,
      status,
      admin_uuid: userInfo.uuid,
      admin_reply: admin_reply?.trim(),
    });

    return respData({
      success: true,
      message: "Feedback updated successfully",
      updated_by: userInfo.email,
      updated_at: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Update feedback error:", error);
    return respErr("Failed to update feedback", 500);
  }
}
