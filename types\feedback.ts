export interface Feedback {
  id?: number;
  uuid?: string;
  created_at?: string;
  user_uuid?: string;
  user_email?: string;
  feedback_type: string;
  rating?: number;
  title?: string;
  content: string;
  status: string;
  admin_reply?: string;
  admin_reply_at?: string;
  admin_uuid?: string;
  metadata?: any;
  user_agent?: string;
  ip_address?: string;
  page_url?: string;
  resolved_at?: string;
}

export enum FeedbackType {
  Bug = "bug",
  Feature = "feature",
  General = "general",
  Complaint = "complaint",
  Suggestion = "suggestion",
  Praise = "praise",
}

export enum FeedbackStatus {
  Pending = "pending",
  InProgress = "in_progress",
  Resolved = "resolved",
  Closed = "closed",
  Rejected = "rejected",
}
