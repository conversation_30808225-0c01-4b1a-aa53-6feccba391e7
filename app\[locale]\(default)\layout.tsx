import Footer from "@/components/blocks/footer";
import Header from "@/components/blocks/header";
import FeedbackComponent from "@/components/blocks/feedback";
import { ReactNode } from "react";
import { getLandingPage } from "@/services/page";

export default async function DefaultLayout({
  children,
  params: { locale },
}: {
  children: ReactNode;
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  return (
    <div className="min-h-screen flex flex-col">
      {page.header && <Header header={page.header} />}
      <main className="overflow-x-hidden flex-1 pt-20">{children}</main>
      {page.footer && <Footer footer={page.footer} />}
      <FeedbackComponent />
    </div>
  );
}
