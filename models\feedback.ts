import { Feedback, FeedbackStatus } from "@/types/feedback";
import { getSupabaseClient } from "./db";

export async function insertFeedback(feedback: Feedback) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.from("feedbacks").insert(feedback);

  if (error) {
    throw error;
  }

  return data;
}

export async function findFeedbackByUuid(
  uuid: string
): Promise<Feedback | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("feedbacks")
    .select("*")
    .eq("uuid", uuid)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function getFeedbacks(
  page: number = 1,
  limit: number = 50,
  status?: string,
  feedback_type?: string
): Promise<Feedback[]> {
  const supabase = getSupabaseClient();
  let query = supabase
    .from("feedbacks")
    .select("*")
    .order("created_at", { ascending: false });

  if (status) {
    query = query.eq("status", status);
  }

  if (feedback_type) {
    query = query.eq("feedback_type", feedback_type);
  }

  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data, error } = await query;

  if (error) {
    throw error;
  }

  return data || [];
}

export async function updateFeedback(uuid: string, feedback: Partial<Feedback>) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("feedbacks")
    .update(feedback)
    .eq("uuid", uuid);

  if (error) {
    throw error;
  }

  return data;
}

export async function getFeedbacksByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 20
): Promise<Feedback[]> {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;
  
  const { data, error } = await supabase
    .from("feedbacks")
    .select("*")
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

export async function getFeedbackStats(): Promise<{
  total: number;
  pending: number;
  resolved: number;
  in_progress: number;
}> {
  const supabase = getSupabaseClient();
  
  const { data: totalData, error: totalError } = await supabase
    .from("feedbacks")
    .select("id", { count: "exact" });

  const { data: pendingData, error: pendingError } = await supabase
    .from("feedbacks")
    .select("id", { count: "exact" })
    .eq("status", FeedbackStatus.Pending);

  const { data: resolvedData, error: resolvedError } = await supabase
    .from("feedbacks")
    .select("id", { count: "exact" })
    .eq("status", FeedbackStatus.Resolved);

  const { data: inProgressData, error: inProgressError } = await supabase
    .from("feedbacks")
    .select("id", { count: "exact" })
    .eq("status", FeedbackStatus.InProgress);

  if (totalError || pendingError || resolvedError || inProgressError) {
    throw new Error("Failed to get feedback stats");
  }

  return {
    total: totalData?.length || 0,
    pending: pendingData?.length || 0,
    resolved: resolvedData?.length || 0,
    in_progress: inProgressData?.length || 0,
  };
}
