import { Apikey } from "@/types/apikey";
import { getSupabaseClient } from "@/models/db";

export enum ApikeyStatus {
  Created = "created",
  Deleted = "deleted",
}



export async function getUserUuidByApiKey(
  apiKey: string
): Promise<string | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("apikeys")
    .select("user_uuid")
    .eq("api_key", apiKey)
    .eq("status", ApikeyStatus.Created)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data?.user_uuid;
}
