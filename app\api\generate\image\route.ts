import { JSONValue, experimental_generateImage as generateImage } from "ai";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { deductCreditsWithTransaction, refundCredits, CreditsTransType } from "@/services/credit";
import { getUuid } from "@/lib/hash";
import { newStorage } from "@/lib/storage";

import type { ImageModelV1 } from "@ai-sdk/provider";
import { kling } from "@/aisdk/kling";
import { openai } from "@ai-sdk/openai";
import { replicate } from "@ai-sdk/replicate";

export async function POST(req: Request) {
  let transactionId: string | null = null;
  let user_uuid: string | null = null;
  let requiredCredits = 0;

  // Helper function to refund credits
  const refundCreditsOnFailure = async (reason: string) => {
    if (transactionId && user_uuid && requiredCredits > 0) {
      try {
        await refundCredits({
          user_uuid,
          credits: requiredCredits,
          originalTransactionId: transactionId,
          reason,
        });
        console.log(`Credits refunded: ${requiredCredits} credits for reason: ${reason}`);
      } catch (refundError) {
        console.error("Failed to refund credits:", refundError);
      }
    }
  };

  try {
    const {
      prompt,
      provider,
      model,
      imageCount = 1,
      highQuality = false,
      ...otherSettings
    } = await req.json();

    if (!prompt || !provider || !model) {
      return respErr("invalid params");
    }

    // Get user UUID
    user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // Calculate required credits
    const baseCredits = 1;
    const qualityMultiplier = highQuality ? 2 : 1;
    requiredCredits = baseCredits * imageCount * qualityMultiplier;

    // Deduct credits before generation
    try {
      const deductionResult = await deductCreditsWithTransaction({
        user_uuid,
        trans_type: CreditsTransType.ImageGeneration,
        credits: requiredCredits,
        description: `Image generation: ${imageCount} image(s), ${highQuality ? 'high quality' : 'standard quality'}`,
      });

      transactionId = deductionResult.transactionId;
      console.log(`Credits deducted: ${requiredCredits} credits, transaction ID: ${transactionId}`);
    } catch (error) {
      console.log("Credit deduction failed:", error);
      return respErr("Insufficient credits or credit deduction failed");
    }

    // Configure image model based on provider
    let imageModel: ImageModelV1;
    let providerOptions: Record<string, Record<string, JSONValue>> = {};

    switch (provider) {
      case "openai":
        imageModel = openai.image(model);
        providerOptions = {
          openai: {
            quality: highQuality ? "hd" : "standard",
            style: "natural",
          },
        };
        break;
      case "replicate":
        imageModel = replicate.image(model);
        providerOptions = {
          replicate: {
            output_quality: highQuality ? 95 : 80,
          },
        };
        break;
      case "kling":
        imageModel = kling.image(model);
        providerOptions = {
          kling: {},
        };
        break;
      default:
        // Refund credits for invalid provider
        await refundCreditsOnFailure("Invalid provider - credits refunded");
        return respErr("invalid provider");
    }

    // Generate images
    let images, warnings;
    try {
      const result = await generateImage({
        model: imageModel,
        prompt: prompt,
        n: imageCount,
        providerOptions,
        ...otherSettings,
      });
      images = result.images;
      warnings = result.warnings;
    } catch (generationError) {
      console.error("Image generation failed:", generationError);
      await refundCreditsOnFailure("Image generation API failed - credits refunded");
      return respErr("Image generation failed");
    }

    if (warnings && warnings.length > 0) {
      console.log("gen images warnings:", provider, warnings);
      // Refund credits for generation warnings
      await refundCreditsOnFailure("Generation warnings - credits refunded");
      return respErr("gen images failed");
    }

    if (!images || images.length === 0) {
      // Refund credits for no images generated
      await refundCreditsOnFailure("No images generated - credits refunded");
      return respErr("No images generated");
    }

    // Upload images to storage
    const storage = newStorage();
    const batch = getUuid();

    const processedImages = await Promise.all(
      images.map(async (image, index) => {
        const filename = `${provider}_image_${batch}_${index}.png`;
        const key = `shipany/${filename}`;
        const body = Buffer.from(image.base64, "base64");

        try {
          const res = await storage.uploadFile({
            body,
            key,
            contentType: "image/png",
            disposition: "inline",
          });

          return {
            ...res,
            provider,
            filename,
            index,
          };
        } catch (err) {
          console.log("upload file failed:", err);
          return {
            provider,
            filename,
            index,
            error: "Upload failed",
          };
        }
      })
    );

    // Check if any uploads failed
    const failedUploads = processedImages.filter(img => img.error);
    if (failedUploads.length > 0) {
      console.log("Some uploads failed:", failedUploads);
      // Note: We don't refund credits for upload failures as images were generated successfully
    }

    return respData({
      images: processedImages,
      transactionId,
      creditsUsed: requiredCredits,
      generationDetails: {
        prompt,
        provider,
        model,
        imageCount,
        highQuality,
      },
    });

  } catch (err) {
    console.error("Unexpected error in image generation:", err);

    // Refund credits if generation failed
    await refundCreditsOnFailure("Unexpected error in generation - credits refunded");

    return respErr("gen image failed");
  }
}
