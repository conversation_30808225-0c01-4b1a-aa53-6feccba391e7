{"metadata": {"title": "Ship Any AI SaaS Startups in hours | ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups, Ship Fast with a variety of templates and components.", "keywords": "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "my_orders": "My Orders"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "credits": {"display": {"loading": "...", "credits_unit": "credits"}, "generation": {"required_credits": "{credits} credits", "insufficient": "Insufficient credits (current: {current})", "checking": "Checking credits...", "multiplier_label": "{count}x"}, "insufficient_modal": {"title": "Insufficient Credits", "description": "You don't have enough credits to complete this generation. Please purchase more credits.", "current_credits": "Current Credits", "required_credits": "Required Credits", "need_more": "Need More", "purchase_button": "Purchase Credits", "later_button": "Maybe Later", "tip": "💡 Purchase credit packages for better value", "close": "Close"}, "admin": {"gift_title": "Gift Credits to User", "gift_description": "Send credits to a user account", "gift_description_with_email": "Send credits to {email}", "gift_breadcrumb": "Gift Credits", "email_label": "User Email", "email_placeholder": "<EMAIL>", "email_tip": "Enter the email address of the user you want to gift credits to", "amount_label": "Credits Amount", "amount_placeholder": "100", "amount_tip": "Enter the number of credits to gift (1-10000)", "reason_label": "Reason", "reason_placeholder": "Reason for gifting credits...", "reason_tip": "Optional: Provide a reason for this credit gift", "expiration_label": "Expiration Date", "expiration_tip": "Optional: Set custom expiration date (ISO format). Leave empty for default (1 year)", "gift_button": "Gift Credits", "gift_action": "🎁 Gift", "gift_credits_action": "🎁 Gift Credits", "success_message": "Successfully gifted {credits} credits to {email}", "credits_column": "Credits", "actions_column": "Actions"}, "errors": {"network_error": "Network connection issue, please check your network and try again", "timeout_error": "Request timeout, please try again later", "connection_error": "Unable to connect to server, please check your network connection", "unauthorized": "Please log in first to perform this operation", "forbidden": "You don't have permission to perform this operation", "session_expired": "Session expired, please log in again", "insufficient_credits": "Insufficient credits, please purchase more credits", "credit_deduction_failed": "Credit deduction failed, please try again later", "credit_refund_failed": "Credit refund failed, please contact support", "credit_fetch_failed": "Failed to fetch credit information, please refresh the page", "validation_error": "Input information is incorrect, please check and try again", "generation_failed": "Image generation failed, please try again", "upload_failed": "File upload failed, please try again", "server_error": "Server error, please try again later", "database_error": "Data processing error, please try again later", "service_unavailable": "Service temporarily unavailable, please try again later", "unknown_error": "Unknown error occurred, please try again later", "retry_button": "Retry", "refresh_button": "Refresh Page", "login_button": "Log In Again", "contact_support": "Contact Support", "fetch_failed": "Fetch Failed"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More"}, "pricing": {"monthly": "Monthly", "yearly": "Yearly", "popular": "Popular", "includes": "Includes", "credits_validity": "{credits} credits, valid for {months} month{plural}", "credits_validity_single": "{credits} credits, valid for {months} month", "purchase_addon": "Purchase Add-on Package", "purchase_immediately": "One-time purchase, effective immediately!", "need_more_credits": "Need more credits?", "prefer_subscription": "Prefer subscription plans?", "addon_packages": "Add-on Packages", "back_to_pricing": "Back to Pricing", "cny_payment": "CNY Payment 👉", "addon_items": {"small": {"title": "Small Package", "description": "Perfect for light users", "feature_0": "50 credits", "feature_1": "1 month validity", "feature_2": "Basic feature support", "feature_3": "Email customer support"}, "medium": {"title": "Medium Package", "description": "Perfect for moderate users", "feature_0": "120 credits", "feature_1": "2 months validity", "feature_2": "Advanced feature support", "feature_3": "Priority customer support", "feature_4": "API access"}, "large": {"title": "Large Package", "description": "Perfect for heavy users", "feature_0": "250 credits", "feature_1": "3 months validity", "feature_2": "Full feature support", "feature_3": "Dedicated customer support", "feature_4": "API access", "feature_5": "Priority processing queue"}}}}