"use client";

import { useEffect, useState } from "react";
import { Progress } from "@/components/ui/progress";
import { Clock, Zap, CheckCircle, XCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { ImageSlotStatus } from "@/types/image-results";

interface ImageSlotProgressProps {
  slotStatus: ImageSlotStatus;
  className?: string;
  translations?: {
    status?: {
      waiting?: string;
      countdown?: string;
      generating?: string;
      completed?: string;
      failed?: string;
      preparing?: string;
    };
    messages?: {
      countdown_seconds?: string;
      slot_number?: string;
      retry?: string;
      generation_error?: string;
      generation_failed?: string;
    };
  };
}

export default function ImageSlotProgress({ slotStatus, className, translations }: ImageSlotProgressProps) {
  const [currentCountdown, setCurrentCountdown] = useState(slotStatus.countdown || 0);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    setCurrentCountdown(slotStatus.countdown || 0);
  }, [slotStatus.countdown]);

  useEffect(() => {
    if (slotStatus.status === 'countdown' && currentCountdown > 0) {
      const timer = setInterval(() => {
        setCurrentCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [slotStatus.status, currentCountdown]);

  useEffect(() => {
    if (slotStatus.status === 'countdown' && (slotStatus.countdown || 0) > 0) {
      const progressValue = (((slotStatus.countdown || 0) - currentCountdown) / (slotStatus.countdown || 1)) * 100;
      setProgress(progressValue);
    } else if (slotStatus.status === 'generating') {
      // Simulate generation progress
      const timer = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 5;
        });
      }, 500);

      return () => clearInterval(timer);
    } else if (slotStatus.status === 'completed') {
      setProgress(100);
    } else {
      setProgress(0);
    }
  }, [slotStatus.status, slotStatus.countdown, currentCountdown]);

  const getStatusInfo = () => {
    // 如果有自定义消息，优先使用
    const customMessage = slotStatus.message;

    // 根据 isActive 状态决定视觉样式
    const isActive = slotStatus.isActive !== false; // 默认为 true，除非明确设置为 false
    const opacity = isActive ? '' : 'opacity-50'; // 非活动状态使用半透明
    const brightness = isActive ? '' : 'brightness-75'; // 非活动状态降低亮度

    switch (slotStatus.status) {
      case 'waiting':
        return {
          icon: Clock,
          title: customMessage || (translations?.status?.waiting || '等待中'),
          description: customMessage ? (translations?.messages?.slot_number?.replace('{number}', String(slotStatus.index + 1)) || `第 ${slotStatus.index + 1} 张`) : (translations?.messages?.slot_number?.replace('{number}', String(slotStatus.index + 1)) || `第 ${slotStatus.index + 1} 张`),
          color: isActive ? 'text-yellow-500' : 'text-gray-400',
          bgColor: isActive ? 'bg-yellow-500/20' : 'bg-gray-500/10',
          borderColor: isActive ? 'border-yellow-500/30' : 'border-gray-500/20',
          containerClass: `${opacity} ${brightness}`,
        };
      case 'countdown':
        const countdownText = translations?.status?.countdown || '倒计时中';
        const secondsText = translations?.messages?.countdown_seconds || '秒';
        return {
          icon: Clock,
          title: currentCountdown > 0 ? `${countdownText} ${currentCountdown}${secondsText}` : countdownText,
          description: customMessage || (translations?.messages?.slot_number?.replace('{number}', String(slotStatus.index + 1)) || `第 ${slotStatus.index + 1} 张`),
          color: isActive ? 'text-blue-500' : 'text-gray-400',
          bgColor: isActive ? 'bg-blue-500/20' : 'bg-gray-500/10',
          borderColor: isActive ? 'border-blue-500/30' : 'border-gray-500/20',
          containerClass: `${opacity} ${brightness}`,
          isHighlight: isActive, // 高亮状态，用于特殊样式
        };
      case 'generating':
        return {
          icon: Zap,
          title: translations?.status?.generating || '生成中',
          description: translations?.messages?.slot_number?.replace('{number}', String(slotStatus.index + 1)) || `第 ${slotStatus.index + 1} 张`,
          color: 'text-green-500',
          bgColor: 'bg-green-500/20',
          borderColor: 'border-green-500/30',
          containerClass: '',
          isHighlight: true,
        };
      case 'completed':
        return {
          icon: CheckCircle,
          title: translations?.status?.completed || '已完成',
          description: translations?.messages?.slot_number?.replace('{number}', String(slotStatus.index + 1)) || `第 ${slotStatus.index + 1} 张`,
          color: 'text-green-600',
          bgColor: 'bg-green-600/20',
          borderColor: 'border-green-600/30',
          containerClass: '',
        };
      case 'failed':
        return {
          icon: XCircle,
          title: translations?.status?.failed || '生成失败',
          description: slotStatus.error || (translations?.messages?.retry || '请重试'),
          color: 'text-red-500',
          bgColor: 'bg-red-500/20',
          borderColor: 'border-red-500/30',
          containerClass: '',
        };
      default:
        return {
          icon: Clock,
          title: translations?.status?.preparing || '准备中',
          description: translations?.messages?.slot_number?.replace('{number}', String(slotStatus.index + 1)) || `第 ${slotStatus.index + 1} 张`,
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/20',
          borderColor: 'border-gray-500/30',
          containerClass: '',
        };
    }
  };

  const statusInfo = getStatusInfo();
  const IconComponent = statusInfo.icon;

  return (
    <div className={cn(
      "relative aspect-square w-full rounded-lg border-2 backdrop-blur-md overflow-hidden transition-all duration-500",
      statusInfo.bgColor,
      statusInfo.borderColor,
      statusInfo.containerClass,
      // 高亮效果 - 当前活动的倒计时或生成中状态
      statusInfo.isHighlight && "ring-2 ring-white/50 shadow-lg scale-105",
      className
    )}>
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent" />
      </div>

      {/* Content */}
      <div className="relative h-full flex flex-col p-4 text-center">
        {/* 顶部状态信息 */}
        <div className="mb-4">
          {/* 状态标题 */}
          <div className={cn("text-lg font-bold mb-1", statusInfo.color)}>
            {statusInfo.title}
          </div>
          {/* 状态描述 */}
          <div className={cn(
            "text-sm",
            statusInfo.isHighlight ? "text-white/90" : "text-white/50"
          )}>
            {statusInfo.description}
          </div>
        </div>

        {/* 中央内容区域 - 统一的布局结构 */}
        <div className="flex-1 flex items-center justify-center">
          {/* 等待状态 - 显示图标 */}
          {slotStatus.status === 'waiting' && (
            <div className={cn("p-4 rounded-full", statusInfo.bgColor)}>
              <IconComponent className={cn("w-8 h-8", statusInfo.color)} />
            </div>
          )}

          {/* 倒计时状态 - 显示转圈加载动画 */}
          {slotStatus.status === 'countdown' && (
            <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin"></div>
          )}

          {/* 生成中状态 - 显示转圈动画 */}
          {slotStatus.status === 'generating' && (
            <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin"></div>
          )}

          {/* 已完成状态 - 显示成功图标 */}
          {slotStatus.status === 'completed' && (
            <div className={cn("p-4 rounded-full", statusInfo.bgColor)}>
              <IconComponent className={cn("w-8 h-8", statusInfo.color)} />
            </div>
          )}

          {/* 失败状态 - 显示错误图标 */}
          {slotStatus.status === 'failed' && (
            <div className={cn("p-4 rounded-full", statusInfo.bgColor)}>
              <IconComponent className={cn("w-8 h-8", statusInfo.color)} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
