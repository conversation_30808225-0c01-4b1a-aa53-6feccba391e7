"use client";

import * as React from "react";
import { useRouter } from "next/navigation";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { CreditCard, Coins } from "lucide-react";

interface InsufficientCreditsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentCredits: number;
  requiredCredits: number;
}

export default function InsufficientCreditsModal({
  open,
  onOpenChange,
  currentCredits,
  requiredCredits,
}: InsufficientCreditsModalProps) {
  const t = useTranslations("credits.insufficient_modal");
  const router = useRouter();
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const handleGoToPricing = () => {
    onOpenChange(false);
    router.push("/#pricing");
  };

  const ModalContent = ({ className }: { className?: string }) => (
    <div className={cn("grid items-start gap-6", className)}>
      {/* 图标和标题区域 */}
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="relative">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <Coins className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {t("title")}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t("description")}
          </p>
        </div>
      </div>

      {/* 积分对比显示 */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 dark:text-gray-400">{t("current_credits")}</span>
          <span className="font-semibold text-blue-600">{currentCredits}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 dark:text-gray-400">{t("required_credits")}</span>
          <span className="font-semibold text-red-600">{requiredCredits}</span>
        </div>
        <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{t("need_more")}</span>
            <span className="font-bold text-red-600">{requiredCredits - currentCredits}</span>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="space-y-3">
        <Button
          onClick={handleGoToPricing}
          className="w-full flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          <CreditCard className="w-4 h-4" />
          {t("purchase_button")}
        </Button>

        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          className="w-full"
        >
          {t("later_button")}
        </Button>
      </div>

      {/* 提示信息 */}
      <div className="text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {t("tip")}
        </p>
      </div>
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader className="sr-only">
            <DialogTitle>{t("title")}</DialogTitle>
            <DialogDescription>
              {t("description")}
            </DialogDescription>
          </DialogHeader>
          <ModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <DrawerHeader className="sr-only">
          <DrawerTitle>{t("title")}</DrawerTitle>
          <DrawerDescription>
            {t("description")}
          </DrawerDescription>
        </DrawerHeader>
        <ModalContent className="px-4" />
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">{t("close")}</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
