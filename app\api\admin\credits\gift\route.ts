import { NextRequest } from "next/server";
import { getUserInfo } from "@/services/user";
import { findUserByEmail } from "@/models/user";
import { increaseCredits, CreditsTransType } from "@/services/credit";
import { respData, respErr } from "@/lib/resp";
import { getOneYearLaterTimestr } from "@/lib/time";

/**
 * POST /api/admin/credits/gift
 * Admin gift credits to a user
 */
export async function POST(req: NextRequest) {
  try {
    // 验证管理员权限
    const adminInfo = await getUserInfo();
    if (!adminInfo || !adminInfo.email) {
      return respErr("Unauthorized", 401);
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(adminInfo.email)) {
      return respErr("Access denied", 403);
    }

    // 解析请求参数
    const { userEmail, credits, reason, expiredAt } = await req.json();

    // 验证参数
    if (!userEmail || typeof userEmail !== 'string') {
      return respErr("User email is required", 400);
    }

    if (typeof credits !== 'number' || credits <= 0) {
      return respErr("Invalid credits amount", 400);
    }

    if (credits > 10000) {
      return respErr("Credits amount cannot exceed 10000", 400);
    }

    // 查找目标用户
    const targetUser = await findUserByEmail(userEmail);
    if (!targetUser) {
      return respErr("User not found", 404);
    }

    // 设置过期时间（默认一年后）
    const finalExpiredAt = expiredAt || getOneYearLaterTimestr();

    // 赠送积分
    try {
      await increaseCredits({
        user_uuid: targetUser.uuid || "",
        trans_type: CreditsTransType.AdminGift,
        credits: credits,
        expired_at: finalExpiredAt,
        order_no: `admin_gift_${adminInfo.email}_${Date.now()}`,
      });

      // 记录操作日志
      console.log(`Admin gift credits: ${adminInfo.email} gifted ${credits} credits to ${userEmail}, reason: ${reason || 'No reason provided'}`);

      return respData({
        success: true,
        message: "Credits gifted successfully",
        details: {
          adminEmail: adminInfo.email,
          targetUserEmail: userEmail,
          targetUserUuid: targetUser.uuid,
          giftedCredits: credits,
          reason: reason || "Admin gift",
          expiredAt: finalExpiredAt,
          timestamp: new Date().toISOString(),
        },
      });

    } catch (error) {
      console.error("Failed to gift credits:", error);
      return respErr("Failed to gift credits", 500);
    }

  } catch (error) {
    console.error("Admin gift credits error:", error);
    return respErr("Internal server error", 500);
  }
}

/**
 * GET /api/admin/credits/gift
 * Get admin gift credits history (optional feature for future)
 */
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const adminInfo = await getUserInfo();
    if (!adminInfo || !adminInfo.email) {
      return respErr("Unauthorized", 401);
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(adminInfo.email)) {
      return respErr("Access denied", 403);
    }

    // 这里可以实现获取赠送历史记录的功能
    // 暂时返回空数组，未来可以扩展
    return respData({
      giftHistory: [],
      message: "Gift history feature not implemented yet",
    });

  } catch (error) {
    console.error("Get gift history error:", error);
    return respErr("Internal server error", 500);
  }
}
