-- 积分系统数据库性能优化索引建议
-- 这些索引可以显著提升积分系统的查询性能

-- 1. 积分表 (credits) 的性能优化索引
-- 用于快速查询用户的有效积分
CREATE INDEX IF NOT EXISTS idx_credits_user_expired 
ON credits (user_uuid, expired_at) 
WHERE expired_at >= NOW();

-- 用于按交易号查询积分记录
CREATE INDEX IF NOT EXISTS idx_credits_trans_no 
ON credits (trans_no);

-- 用于按订单号查询积分记录
CREATE INDEX IF NOT EXISTS idx_credits_order_no 
ON credits (order_no) 
WHERE order_no IS NOT NULL AND order_no != '';

-- 用于按交易类型和时间查询
CREATE INDEX IF NOT EXISTS idx_credits_trans_type_created 
ON credits (trans_type, created_at DESC);

-- 2. 订单表 (orders) 的性能优化索引
-- 用于快速查询用户的首次付费订单
CREATE INDEX IF NOT EXISTS idx_orders_user_status_created 
ON orders (user_uuid, status, created_at) 
WHERE status = 'paid';

-- 用于按订单号查询
CREATE INDEX IF NOT EXISTS idx_orders_order_no 
ON orders (order_no);

-- 3. 用户表 (users) 的性能优化索引
-- 用于管理员按邮箱搜索用户
CREATE INDEX IF NOT EXISTS idx_users_email_ilike 
ON users USING gin (email gin_trgm_ops);

-- 用于按创建时间排序
CREATE INDEX IF NOT EXISTS idx_users_created_at 
ON users (created_at DESC);

-- 4. 反馈表 (feedbacks) 的性能优化索引
-- 用于按状态和创建时间查询
CREATE INDEX IF NOT EXISTS idx_feedbacks_status_created 
ON feedbacks (status, created_at DESC);

-- 用于按用户查询反馈
CREATE INDEX IF NOT EXISTS idx_feedbacks_user_uuid 
ON feedbacks (user_uuid);

-- 5. 复合索引优化建议
-- 积分表的复合索引，用于复杂查询
CREATE INDEX IF NOT EXISTS idx_credits_user_type_expired 
ON credits (user_uuid, trans_type, expired_at) 
WHERE expired_at >= NOW();

-- 6. 部分索引优化（仅索引有效数据）
-- 只索引有效的积分记录
CREATE INDEX IF NOT EXISTS idx_credits_valid_only 
ON credits (user_uuid, credits, expired_at) 
WHERE expired_at >= NOW() AND credits > 0;

-- 只索引已付费的订单
CREATE INDEX IF NOT EXISTS idx_orders_paid_only 
ON orders (user_uuid, created_at) 
WHERE status = 'paid';

-- 7. 统计信息更新建议
-- 定期更新表的统计信息以优化查询计划
-- ANALYZE credits;
-- ANALYZE orders;
-- ANALYZE users;
-- ANALYZE feedbacks;

-- 8. 性能监控查询
-- 查看最慢的查询
-- SELECT query, mean_time, calls, total_time 
-- FROM pg_stat_statements 
-- WHERE query LIKE '%credits%' 
-- ORDER BY mean_time DESC 
-- LIMIT 10;

-- 查看索引使用情况
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes 
-- WHERE tablename IN ('credits', 'orders', 'users', 'feedbacks')
-- ORDER BY idx_scan DESC;

-- 9. 缓存配置建议
-- 在应用层面：
-- - 使用 30 秒的内存缓存来缓存用户积分信息
-- - 在积分变更时立即清除相关缓存
-- - 使用批量查询减少数据库连接数

-- 10. 查询优化建议
-- - 使用批量查询替代 N+1 查询模式
-- - 在管理员页面使用分页和搜索过滤
-- - 避免在高频操作中进行复杂的聚合查询
-- - 使用适当的 LIMIT 限制结果集大小

-- 注意事项：
-- 1. 在生产环境中创建索引时，建议使用 CONCURRENTLY 选项避免锁表
-- 2. 定期监控索引的使用情况，删除未使用的索引
-- 3. 根据实际查询模式调整索引策略
-- 4. 考虑使用连接池来减少数据库连接开销
