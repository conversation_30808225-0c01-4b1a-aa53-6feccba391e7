"use client";

import { useState, useEffect, useRef } from "react";
import ImageResultContainer from "@/components/blocks/image-results/ImageResultContainer";
import ImagePreviewModal from "@/components/blocks/image-results/ImagePreviewModal";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageData, GenerationResult, ImageSlotStatus } from "@/types/image-results";

export default function ImageResultsShowcase() {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImages, setPreviewImages] = useState<ImageData[]>([]);
  const [previewIndex, setPreviewIndex] = useState(0);
  
  // 实时模拟状态
  const [isSimulating, setIsSimulating] = useState(false);
  const [simulationCountdown, setSimulationCountdown] = useState(8);
  const [currentImageIndex, setCurrentImageIndex] = useState(0); // 当前正在处理的图片索引
  const [completedSlots, setCompletedSlots] = useState<{[key: number]: ImageData}>({}); // 已完成的图片槽位
  const [failedSlots, setFailedSlots] = useState<{[key: number]: string}>({}); // 失败的图片槽位
  const [simulationType, setSimulationType] = useState<'success' | 'early-fail' | 'partial-fail' | 'limited'>('success'); // 模拟类型
  const [totalSlots, setTotalSlots] = useState(4); // 总图片槽位数量

  // 模拟图片数据 - 使用稳定的内联SVG
  const sampleImages: ImageData[] = [
    {
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSIjNjM2NkYxIi8+Cjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+U2FtcGxlIDEgKE9wZW5BSSk8L3RleHQ+Cjwvc3ZnPg==",
      filename: "sample-1.png",
      size: 524288,
      width: 512,
      height: 512,
      provider: "openai",
    },
    {
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSIjMTBCOTgxIi8+Cjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+U2FtcGxlIDIgKFJlcGxpY2F0ZSk8L3RleHQ+Cjwvc3ZnPg==", 
      filename: "sample-2.png",
      size: 498432,
      width: 512,
      height: 512,
      provider: "replicate",
    },
    {
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSIjRjU5RTBCIi8+Cjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+U2FtcGxlIDMgKEtsaW5nKTwvdGV4dD4KPHN2Zz4=",
      filename: "sample-3.png", 
      size: 512000,
      width: 512,
      height: 512,
      provider: "kling",
    },
    {
      url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSIjRUY0NDQ0Ii8+Cjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+U2FtcGxlIDQgKE9wZW5BSSk8L3RleHQ+Cjwvc3ZnPg==",
      filename: "sample-4.png",
      size: 487424,
      width: 512,
      height: 512,
      provider: "openai",
    }
  ];

  // 定时器引用
  const simulationTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 开始模拟
  const startSimulation = (type: 'success' | 'early-fail' | 'partial-fail' | 'limited' = 'success') => {
    setIsSimulating(true);
    setCurrentImageIndex(0);
    setCompletedSlots({});
    setFailedSlots({});
    setSimulationType(type);
    setSimulationCountdown(8); // 8秒：5秒倒计时 + 3秒生成

    // 根据模拟类型设置总槽位数
    switch (type) {
      case 'limited':
        setTotalSlots(2); // 限量生成只显示2个槽位
        break;
      default:
        setTotalSlots(4); // 其他情况显示4个槽位
        break;
    }

    simulationTimerRef.current = setInterval(() => {
      setSimulationCountdown(prev => {
        if (prev <= 1) {
          // 当前图片处理完成
          setCurrentImageIndex(prevIndex => {
            const currentIndex = prevIndex;

            // 根据模拟类型决定是否失败
            let shouldFail = false;
            let errorMessage = '';

            switch (type) {
              case 'early-fail':
                // 第一张就失败
                if (currentIndex === 0) {
                  shouldFail = true;
                  errorMessage = '内容违反使用政策';
                }
                break;
              case 'partial-fail':
                // 第2和第4张失败
                if (currentIndex === 1 || currentIndex === 3) {
                  shouldFail = true;
                  errorMessage = currentIndex === 1 ? '网络超时' : '服务器错误';
                }
                break;
              case 'limited':
                // 只生成2张图片
                if (currentIndex >= 2) {
                  setIsSimulating(false);
                  if (simulationTimerRef.current) {
                    clearInterval(simulationTimerRef.current);
                  }
                  return currentIndex;
                }
                break;
            }

            if (shouldFail) {
              // 添加到失败列表
              setFailedSlots(prev => ({
                ...prev,
                [currentIndex]: errorMessage
              }));

              // 如果是早期失败，停止整个流程
              if (type === 'early-fail') {
                setIsSimulating(false);
                if (simulationTimerRef.current) {
                  clearInterval(simulationTimerRef.current);
                }
                return currentIndex;
              }
            } else {
              // 添加到完成槽位
              setCompletedSlots(prev => ({
                ...prev,
                [currentIndex]: sampleImages[currentIndex]
              }));
            }

            // 检查是否所有图片都处理完了
            const nextIndex = currentIndex + 1;
            if (nextIndex >= totalSlots) {
              setIsSimulating(false);
              if (simulationTimerRef.current) {
                clearInterval(simulationTimerRef.current);
              }
              return currentIndex;
            }

            return nextIndex;
          });

          return 8; // 重置为8秒（5秒倒计时 + 3秒生成）
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 停止模拟
  const stopSimulation = () => {
    setIsSimulating(false);
    if (simulationTimerRef.current) {
      clearInterval(simulationTimerRef.current);
    }
  };

  // 重置模拟
  const resetSimulation = () => {
    stopSimulation();
    setCurrentImageIndex(0);
    setCompletedSlots({});
    setFailedSlots({});
    setSimulationCountdown(8);
    setSimulationType('success');
    setTotalSlots(4);
  };

  const handleImageClick = (image: ImageData, index: number) => {
    setPreviewImages(sampleImages);
    setPreviewIndex(index);
    setPreviewOpen(true);
  };

  // 创建模拟的 GenerationResult 对象
  const createSimulationResult = (): GenerationResult => {
    const imageSlots: ImageSlotStatus[] = [];

    for (let i = 0; i < totalSlots; i++) {
      if (failedSlots[i]) {
        // 失败的图片
        imageSlots.push({
          index: i,
          status: 'failed',
          error: failedSlots[i],
        });
      } else if (completedSlots[i]) {
        // 已完成的图片
        imageSlots.push({
          index: i,
          status: 'completed',
          image: completedSlots[i],
        });
      } else if (i === currentImageIndex && isSimulating) {
        // 当前正在处理的图片 - 高亮状态，视觉焦点
        if (simulationCountdown > 3) {
          // 倒计时阶段（8-4秒）
          const countdownTime = simulationCountdown - 3; // 实际倒计时时间
          const timeText = `${countdownTime}秒`;

          imageSlots.push({
            index: i,
            status: 'countdown',
            countdown: countdownTime,
            message: `预计 ${timeText} 后开始...`,
            isActive: true, // 标记为活动状态，用于高亮显示
          });
        } else {
          // 生成阶段（3-1秒）
          imageSlots.push({
            index: i,
            status: 'generating',
            isActive: true,
          });
        }
      } else {
        // 等待处理的图片 - 灰色半透明状态
        imageSlots.push({
          index: i,
          status: 'waiting',
          message: '等待处理',
          isActive: false, // 标记为非活动状态，用于灰色显示
        });
      }
    }

    // 确定整体状态
    let overallStatus: 'waiting' | 'generating' | 'completed' | 'failed' = 'waiting';
    const completedCount = Object.keys(completedSlots).length;
    const failedCount = Object.keys(failedSlots).length;

    if (isSimulating) {
      overallStatus = 'generating';
    } else if (simulationType === 'early-fail' && failedCount > 0) {
      overallStatus = 'failed';
    } else if (completedCount > 0 || failedCount > 0) {
      overallStatus = 'completed';
    }

    return {
      id: 'simulation-demo',
      timestamp: new Date(),
      prompt: "一只可爱的卡通猫咪在花园里玩耍",
      settings: {
        provider: 'openai',
        ratio: '1:1',
        style: '卡通',
      },
      images: Object.values(completedSlots),
      status: overallStatus,
      error: simulationType === 'early-fail' && failedCount > 0
        ? Object.values(failedSlots)[0]
        : undefined,
      mode: 'text-to-image',
      imageSlots,
    };
  };

  const createErrorResult = (): GenerationResult => ({
    id: 'error-demo',
    timestamp: new Date(),
    prompt: "违反内容政策的提示词",
    settings: {
      provider: 'openai',
      ratio: '1:1',
    },
    images: [],
    status: 'failed',
    error: "内容违反了我们的使用政策，请修改您的提示词后重试。",
    mode: 'text-to-image',
    imageSlots: [
      { index: 0, status: 'failed', error: "内容政策违规" },
      { index: 1, status: 'failed', error: "内容政策违规" },
      { index: 2, status: 'failed', error: "内容政策违规" },
      { index: 3, status: 'failed', error: "内容政策违规" },
    ],
  });

  const createPartialErrorResult = (): GenerationResult => ({
    id: 'partial-error-demo',
    timestamp: new Date(),
    prompt: "一只可爱的卡通猫咪在花园里玩耍",
    settings: {
      provider: 'openai',
      ratio: '1:1',
      style: '卡通',
    },
    images: [sampleImages[0], sampleImages[1]],
    status: 'completed',
    mode: 'text-to-image',
    imageSlots: [
      { index: 0, status: 'completed', image: sampleImages[0] },
      { index: 1, status: 'completed', image: sampleImages[1] },
      { index: 2, status: 'failed', error: "网络超时，请重试" },
      { index: 3, status: 'failed', error: "服务器错误，请稍后重试" },
    ],
  });

  // 清理定时器
  useEffect(() => {
    return () => {
      if (simulationTimerRef.current) {
        clearInterval(simulationTimerRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-brand-light mb-4">
            图片生成结果组件演示
          </h1>
          <p className="text-xl text-brand-light/80 max-w-3xl mx-auto">
            实时状态模拟演示，包含完整流程和错误处理
          </p>
        </div>

        {/* 实时状态模拟演示 */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-brand-light mb-6">
            实时状态模拟演示
          </h2>
          <div className="space-y-6">
            {/* 控制面板 */}
            <Card className="homepage-section-bg border-white/20">
              <CardHeader>
                <CardTitle className="text-brand-light">实时模拟控制</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={() => startSimulation('success')}
                      disabled={isSimulating}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {isSimulating && simulationType === 'success' ? "正常模拟中..." : "正常流程"}
                    </Button>
                    <Button
                      onClick={() => startSimulation('early-fail')}
                      disabled={isSimulating}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {isSimulating && simulationType === 'early-fail' ? "早期失败中..." : "早期失败"}
                    </Button>
                    <Button
                      onClick={() => startSimulation('partial-fail')}
                      disabled={isSimulating}
                      className="bg-orange-600 hover:bg-orange-700"
                    >
                      {isSimulating && simulationType === 'partial-fail' ? "部分失败中..." : "部分失败"}
                    </Button>
                    <Button
                      onClick={() => startSimulation('limited')}
                      disabled={isSimulating}
                      className="bg-yellow-600 hover:bg-yellow-700"
                    >
                      {isSimulating && simulationType === 'limited' ? "限量生成中..." : "限量生成"}
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={stopSimulation}
                      disabled={!isSimulating}
                      variant="outline"
                      className="border-white/20 text-brand-light hover:bg-white/10"
                    >
                      停止模拟
                    </Button>
                    <Button
                      onClick={resetSimulation}
                      variant="outline"
                      className="border-white/20 text-brand-light hover:bg-white/10"
                    >
                      重置
                    </Button>
                    <Button
                      onClick={() => {
                        setPreviewImages(sampleImages);
                        setPreviewIndex(0);
                        setPreviewOpen(true);
                      }}
                      variant="outline"
                      className="border-white/20 text-brand-light hover:bg-white/10"
                    >
                      预览图片
                    </Button>
                  </div>
                </div>

                {isSimulating && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm text-brand-light/70">
                      <span>当前图片: {currentImageIndex + 1}/{totalSlots}</span>
                      <span>倒计时: {simulationCountdown}秒</span>
                      <span>模式: {simulationType}</span>
                    </div>
                    <div className="flex justify-between text-xs text-brand-light/50">
                      <span>已完成: {Object.keys(completedSlots).length}</span>
                      <span>已失败: {Object.keys(failedSlots).length}</span>
                      <span>阶段: {simulationCountdown > 3 ? '倒计时' : '生成中'}</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${(Object.keys(completedSlots).length / totalSlots) * 100}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 实时模拟容器 */}
            <div className="w-full">
              <h3 className="text-lg font-semibold text-brand-light mb-4">
                全流程模拟 - 倒计时 → 生成 → 下一张倒计时 → 生成，以此类推
              </h3>
              <ImageResultContainer
                result={createSimulationResult()}
              />
            </div>
          </div>
        </section>

        {/* 错误状态模拟演示 */}
        <section>
          <h2 className="text-2xl font-bold text-brand-light mb-6">
            错误状态模拟演示
          </h2>
          <div className="space-y-8">
            {/* 整体失败 */}
            <div className="w-full">
              <h3 className="text-lg font-semibold text-brand-light mb-4">
                整体失败 - 内容政策违规
              </h3>
              <ImageResultContainer
                result={createErrorResult()}
              />
            </div>

            {/* 部分失败 */}
            <div className="w-full">
              <h3 className="text-lg font-semibold text-brand-light mb-4">
                部分失败 - 技术错误
              </h3>
              <ImageResultContainer
                result={createPartialErrorResult()}
              />
            </div>
          </div>
        </section>

        {/* 图片预览模态框 */}
        <ImagePreviewModal
          isOpen={previewOpen}
          onClose={() => setPreviewOpen(false)}
          images={previewImages}
          currentIndex={previewIndex}
          onIndexChange={setPreviewIndex}
        />
      </div>
    </div>
  );
}
