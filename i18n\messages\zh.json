{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "Ship<PERSON>ny, AI SaaS 模板, NextJS 模板"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "my_orders": "我的订单"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "credits": {"display": {"loading": "...", "credits_unit": "积分"}, "generation": {"required_credits": "{credits} 积分", "insufficient": "积分不足 (当前: {current})", "checking": "检查积分...", "multiplier_label": "{count}x"}, "insufficient_modal": {"title": "积分不足", "description": "您的积分不足以完成此次生成，请购买更多积分", "current_credits": "当前积分", "required_credits": "需要积分", "need_more": "还需要", "purchase_button": "购买积分", "later_button": "稍后再说", "tip": "💡 购买积分包可以享受更优惠的价格", "close": "关闭"}, "admin": {"gift_title": "赠送积分给用户", "gift_description": "向用户账户发送积分", "gift_description_with_email": "向 {email} 发送积分", "gift_breadcrumb": "赠送积分", "email_label": "用户邮箱", "email_placeholder": "<EMAIL>", "email_tip": "输入要赠送积分的用户邮箱地址", "amount_label": "积分数量", "amount_placeholder": "100", "amount_tip": "输入要赠送的积分数量 (1-10000)", "reason_label": "原因", "reason_placeholder": "赠送积分的原因...", "reason_tip": "可选：提供赠送积分的原因", "expiration_label": "过期时间", "expiration_tip": "可选：设置自定义过期时间（ISO格式）。留空为默认（1年）", "gift_button": "赠送积分", "gift_action": "🎁 赠送", "gift_credits_action": "🎁 赠送积分", "success_message": "成功向 {email} 赠送了 {credits} 积分", "credits_column": "积分", "actions_column": "操作"}, "errors": {"network_error": "网络连接出现问题，请检查网络后重试", "timeout_error": "请求超时，请稍后重试", "connection_error": "无法连接到服务器，请检查网络连接", "unauthorized": "请先登录后再进行操作", "forbidden": "您没有权限执行此操作", "session_expired": "登录已过期，请重新登录", "insufficient_credits": "积分不足，请购买更多积分", "credit_deduction_failed": "积分扣除失败，请稍后重试", "credit_refund_failed": "积分退款失败，请联系客服", "credit_fetch_failed": "获取积分信息失败，请刷新页面", "validation_error": "输入信息有误，请检查后重试", "generation_failed": "图片生成失败，请重试", "upload_failed": "文件上传失败，请重试", "server_error": "服务器出现问题，请稍后重试", "database_error": "数据处理出现问题，请稍后重试", "service_unavailable": "服务暂时不可用，请稍后重试", "unknown_error": "出现未知错误，请稍后重试", "retry_button": "重试", "refresh_button": "刷新页面", "login_button": "重新登录", "contact_support": "联系客服", "fetch_failed": "获取失败"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "pricing": {"monthly": "月费", "yearly": "年费", "popular": "热门", "includes": "包含", "credits_validity": "{credits} 积分，有效期 {months} 个月", "credits_validity_single": "{credits} 积分，有效期 {months} 个月", "purchase_addon": "购买加量包", "purchase_immediately": "一次购买，立即生效！", "need_more_credits": "需要更多的积分？", "prefer_subscription": "更加喜欢订阅方案？", "addon_packages": "加量包", "back_to_pricing": "返回价格方案", "cny_payment": "人民币支付 👉", "addon_items": {"small": {"title": "小量包", "description": "适合轻度使用的用户", "feature_0": "50 积分", "feature_1": "1个月有效期", "feature_2": "基础功能支持", "feature_3": "邮件客服支持"}, "medium": {"title": "中量包", "description": "适合中度使用的用户", "feature_0": "120 积分", "feature_1": "2个月有效期", "feature_2": "高级功能支持", "feature_3": "优先客服支持", "feature_4": "API 访问权限"}, "large": {"title": "大量包", "description": "适合重度使用的用户", "feature_0": "250 积分", "feature_1": "3个月有效期", "feature_2": "全功能支持", "feature_3": "专属客服支持", "feature_4": "API 访问权限", "feature_5": "优先处理队列"}}}}