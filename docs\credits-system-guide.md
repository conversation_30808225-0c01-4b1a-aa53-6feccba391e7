# 积分系统完整指南

## 📋 系统概述

本积分系统为AI图片生成网站提供完整的用户积分管理功能，包括积分显示、消耗、充值、管理等核心功能。

## 🏗️ 系统架构

### 核心组件
- **积分服务** (`services/credit.ts`) - 核心业务逻辑
- **积分模型** (`models/credit.ts`) - 数据库操作
- **积分显示组件** (`components/credits/display.tsx`) - 前端显示
- **积分API接口** (`app/api/user/credits/`) - RESTful API

### 数据库表结构
```sql
-- 积分表
CREATE TABLE credits (
  id SERIAL PRIMARY KEY,
  user_uuid VARCHAR(255) NOT NULL,
  credits INTEGER NOT NULL,
  trans_type VARCHAR(50) NOT NULL,
  order_no VARCHAR(255),
  expired_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 性能优化索引
CREATE INDEX idx_credits_user_expired ON credits (user_uuid, expired_at);
CREATE INDEX idx_credits_user_type_expired ON credits (user_uuid, trans_type, expired_at);
```

## 🎯 核心功能

### 1. 积分显示
- **位置**: Header右上角用户菜单
- **显示内容**: 当前可用积分数量
- **更新机制**: 实时更新，30秒缓存

### 2. 积分计算
```typescript
// 积分计算公式
const requiredCredits = imageCount × qualityMultiplier;

// 质量倍数
const qualityMultiplier = enableHighQuality ? 2 : 1;
```

### 3. 积分扣除流程
1. **生成前验证**: 检查登录状态和积分余额
2. **立即扣除**: 点击生成按钮时立即扣除积分
3. **失败退款**: 生成失败时自动退还积分

### 4. 积分不足处理
- 显示详细的积分不足对话框
- 提供当前积分、所需积分、缺少积分信息
- 一键跳转到定价页面购买积分

## 🔧 API接口

### 获取用户积分
```http
GET /api/user/credits
Authorization: Bearer <token>

Response:
{
  "credits": 100,
  "success": true
}
```

### 扣除积分
```http
POST /api/user/credits/deduct
Content-Type: application/json
Authorization: Bearer <token>

{
  "credits": 2,
  "reason": "Image generation",
  "metadata": {
    "imageCount": 1,
    "highQuality": true
  }
}
```

### 退还积分
```http
POST /api/user/credits/refund
Content-Type: application/json
Authorization: Bearer <token>

{
  "credits": 2,
  "reason": "Generation failed",
  "originalOrderNo": "order_123"
}
```

## 👨‍💼 管理员功能

### 用户管理
- **路径**: `/admin/users`
- **功能**: 查看所有用户及其积分余额
- **搜索**: 支持按邮箱搜索用户

### 赠送积分
- **路径**: `/admin/credits/gift`
- **功能**: 管理员向用户赠送积分
- **参数**: 用户邮箱、积分数量、原因、过期时间

### 反馈管理
- **路径**: `/admin/feedback`
- **功能**: 查看和管理用户反馈
- **统计**: 显示反馈总数和待处理数量

## 🌍 国际化支持

### 支持语言
- 中文 (zh)
- 英文 (en)

### 翻译文件
- `messages/zh.json` - 中文翻译
- `messages/en.json` - 英文翻译

### 关键翻译键
```json
{
  "credits": {
    "display": "积分",
    "insufficient": "积分不足",
    "required": "所需积分",
    "current": "当前积分",
    "purchase": "购买积分"
  }
}
```

## ⚡ 性能优化

### 缓存策略
- **内存缓存**: 30秒TTL，减少数据库查询
- **批量查询**: 解决N+1查询问题
- **缓存失效**: 积分操作后自动清除缓存

### 数据库优化
- **索引优化**: 针对常用查询添加复合索引
- **批量操作**: 管理员页面使用批量查询
- **连接池**: 优化数据库连接管理

## 🧪 测试覆盖

### 功能测试
- ✅ 积分显示功能
- ✅ 积分计算逻辑
- ✅ 积分扣除流程
- ✅ 积分不足处理
- ✅ 管理员功能
- ✅ 国际化支持

### 性能测试
- ✅ 缓存机制验证
- ✅ 批量查询优化
- ✅ 数据库索引效果

## 🚀 部署配置

### 环境变量
```env
# 数据库配置
DATABASE_URL=postgresql://user:password@host:port/database

# 认证配置
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://your-domain.com

# 管理员邮箱
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### 数据库迁移
```sql
-- 创建积分表
\i database/migrations/create_credits_table.sql

-- 创建性能索引
\i database/migrations/create_credits_indexes.sql

-- 创建反馈表
\i database/migrations/create_feedbacks_table.sql
```

## 🔍 故障排除

### 常见问题

1. **积分显示不更新**
   - 检查缓存是否正常
   - 验证API接口响应
   - 确认用户登录状态

2. **积分扣除失败**
   - 检查数据库连接
   - 验证事务完整性
   - 查看错误日志

3. **管理员功能无法访问**
   - 确认邮箱在ADMIN_EMAILS中
   - 检查认证状态
   - 验证权限中间件

### 日志监控
- 积分操作日志记录在 `credits` 表的 `trans_type` 字段
- API错误日志通过Next.js内置日志系统
- 性能监控通过数据库查询日志

## 📈 未来扩展

### 计划功能
- 积分有效期管理
- 积分使用统计
- 积分兑换商城
- 积分等级系统

### 技术改进
- Redis缓存集成
- 微服务架构迁移
- 实时积分同步
- 高可用部署

---

**文档版本**: v1.0  
**最后更新**: 2025-08-03  
**维护者**: AI Development Team
