"use client";

import React, { useState } from "react";
import { AIGenerator as AIGeneratorType } from "@/types/blocks/ai-generator";
import { GenerationSettings } from "@/types/image-results";
import AIGenerator from "@/components/blocks/ai-generator";
import ImageResultContainer from "@/components/blocks/image-results/ImageResultContainer";

// 生成参数接口（包含唯一标识符和时间戳）
interface GenerationParams {
  id: string;
  prompt: string;
  settings: GenerationSettings;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string;
  timestamp: number;
}

interface AIGeneratorWithResultsProps {
  generator: AIGeneratorType;
}

export default function AIGeneratorWithResults({ generator }: AIGeneratorWithResultsProps) {
  // 使用数组存储多个生成结果，支持持久化显示
  const [generations, setGenerations] = useState<GenerationParams[]>([]);

  // 处理新的图片生成请求
  const handleGenerate = (params: Omit<GenerationParams, 'id' | 'timestamp'>) => {
    // 创建新的生成参数，包含唯一ID和时间戳
    const newGeneration: GenerationParams = {
      ...params,
      id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    // 将新生成添加到数组开头（最新的在顶部）
    setGenerations(prev => [newGeneration, ...prev]);
  };

  // 处理移除特定生成容器
  const handleRemoveGeneration = (id: string) => {
    setGenerations(prev => prev.filter(gen => gen.id !== id));
  };

  return (
    <section className="py-16">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          {/* AI 生成器组件 */}
          <AIGenerator 
            generator={generator} 
            onGenerate={handleGenerate}
          />

          {/* 多个图片结果容器 - 按时间顺序显示（最新在顶部） */}
          {generations.length > 0 && (
            <div className="mt-8 space-y-8">
              {generations.map((generation) => (
                <div key={generation.id} className="container">
                  <div className="max-w-6xl mx-auto">
                    <ImageResultContainer
                      generationParams={generation}
                      enableCountdown={true}
                      onCancel={() => handleRemoveGeneration(generation.id)}
                      translations={generator.image_results}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
