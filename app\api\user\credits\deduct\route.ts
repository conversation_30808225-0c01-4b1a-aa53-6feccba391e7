import { deductCreditsWithTransaction, CreditsTransType } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { respData, respErr } from "@/lib/resp";

/**
 * POST /api/user/credits/deduct
 * Deduct credits from user account with transaction safety
 */
export async function POST(req: Request) {
  try {
    // Get current user UUID
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("User not authenticated", 401);
    }

    // Parse request body
    const { 
      credits, 
      transType, 
      description,
      metadata 
    } = await req.json();

    // Validate input parameters
    if (typeof credits !== 'number' || credits <= 0) {
      return respErr("Invalid credits amount", 400);
    }

    if (!transType || !Object.values(CreditsTransType).includes(transType)) {
      return respErr("Invalid transaction type", 400);
    }

    // Perform credit deduction with transaction safety
    const deductionResult = await deductCreditsWithTransaction({
      user_uuid,
      trans_type: transType,
      credits,
      description,
    });

    // Return success response with transaction details
    return respData({
      success: true,
      transactionId: deductionResult.transactionId,
      deductedCredits: credits,
      transType,
      description: description || `Credits deducted for ${transType}`,
      metadata: metadata || {},
      remainingCredits: deductionResult.remainingCredits,
      message: "Credits deducted successfully"
    });

  } catch (error) {
    console.error("Credit deduction failed:", error);

    // 根据错误类型返回不同的错误信息
    let errorMessage = "积分扣除失败，请稍后重试";
    let errorCode = 500;

    if (error instanceof Error) {
      const errorMsg = error.message.toLowerCase();

      if (errorMsg.includes('insufficient') || errorMsg.includes('not enough')) {
        errorMessage = "积分不足，无法完成操作";
        errorCode = 400;
      } else if (errorMsg.includes('network') || errorMsg.includes('connection')) {
        errorMessage = "网络连接出现问题，请检查网络后重试";
        errorCode = 503;
      } else if (errorMsg.includes('timeout')) {
        errorMessage = "请求超时，请稍后重试";
        errorCode = 408;
      } else if (errorMsg.includes('database') || errorMsg.includes('db')) {
        errorMessage = "数据处理出现问题，请稍后重试";
        errorCode = 500;
      }
    }

    return respErr(errorMessage, errorCode, {
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
      retryable: errorCode !== 400, // 积分不足时不可重试
    });
  }
}
