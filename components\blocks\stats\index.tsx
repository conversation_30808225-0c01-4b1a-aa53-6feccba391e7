import { Heart, Zap } from "lucide-react";

import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Stats({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-32">
      <div className="container">
        <div className="rounded-lg p-8">
          <div className="flex flex-col items-center gap-4">
            {section.label && (
              <div className="flex items-center gap-1 text-sm font-semibold text-brand-dark border-gray-300">
                {section.icon && (
                  <Icon name={section.icon} className="h-6 w-auto border-gray-300" />
                )}
                {section.label}
              </div>
            )}
            <h2 className="text-center text-3xl font-semibold lg:text-4xl text-white drop-shadow-lg">
              {section.title}
            </h2>
            <p className="text-center lg:text-lg" style={{color: '#475569'}}>
              {section.description}
            </p>
            <div className="w-full grid gap-10 md:grid-cols-3 lg:gap-0 mt-8">
              {section.items?.map((item, index) => {
                return (
                  <div key={index} className="text-center">
                    <p className="text-lg font-semibold" style={{color: '#475569'}}>
                      {item.title}
                    </p>
                    <p className="pt-2 text-7xl font-semibold lg:pt-4 text-orange-500">
                      {item.label}
                    </p>
                    <p className="text-xl mt-2 font-normal" style={{color: '#475569'}}>
                      {item.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
