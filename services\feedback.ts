import { insertFeedback, updateFeedback } from "@/models/feedback";
import { Feedback, FeedbackStatus, FeedbackType } from "@/types/feedback";
import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";

export async function createFeedback({
  user_uuid,
  user_email,
  feedback_type,
  rating,
  title,
  content,
  metadata,
  user_agent,
  ip_address,
  page_url,
}: {
  user_uuid?: string;
  user_email?: string;
  feedback_type: string;
  rating?: number;
  title?: string;
  content: string;
  metadata?: any;
  user_agent?: string;
  ip_address?: string;
  page_url?: string;
}): Promise<Feedback> {
  const feedback: Feedback = {
    uuid: getUuid(),
    created_at: getIsoTimestr(),
    user_uuid,
    user_email,
    feedback_type,
    rating,
    title,
    content,
    status: FeedbackStatus.Pending,
    metadata,
    user_agent,
    ip_address,
    page_url,
  };

  await insertFeedback(feedback);
  return feedback;
}

export async function resolveFeedback({
  uuid,
  admin_uuid,
  admin_reply,
}: {
  uuid: string;
  admin_uuid: string;
  admin_reply?: string;
}): Promise<void> {
  const updateData: Partial<Feedback> = {
    status: FeedbackStatus.Resolved,
    admin_uuid,
    admin_reply,
    admin_reply_at: getIsoTimestr(),
    resolved_at: getIsoTimestr(),
  };

  await updateFeedback(uuid, updateData);
}

export async function updateFeedbackStatus({
  uuid,
  status,
  admin_uuid,
  admin_reply,
}: {
  uuid: string;
  status: string;
  admin_uuid?: string;
  admin_reply?: string;
}): Promise<void> {
  const updateData: Partial<Feedback> = {
    status,
  };

  if (admin_uuid) {
    updateData.admin_uuid = admin_uuid;
  }

  if (admin_reply) {
    updateData.admin_reply = admin_reply;
    updateData.admin_reply_at = getIsoTimestr();
  }

  if (status === FeedbackStatus.Resolved) {
    updateData.resolved_at = getIsoTimestr();
  }

  await updateFeedback(uuid, updateData);
}

export function validateFeedbackType(type: string): boolean {
  return Object.values(FeedbackType).includes(type as FeedbackType);
}

export function validateFeedbackStatus(status: string): boolean {
  return Object.values(FeedbackStatus).includes(status as FeedbackStatus);
}

export function validateRating(rating?: number): boolean {
  if (rating === undefined || rating === null) {
    return true; // Rating is optional
  }
  return Number.isInteger(rating) && rating >= 1 && rating <= 5;
}
