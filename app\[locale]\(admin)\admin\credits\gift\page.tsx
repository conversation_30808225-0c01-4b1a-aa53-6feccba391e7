import Empty from "@/components/blocks/empty";
import FormSlot from "@/components/dashboard/slots/form";
import { Form as FormSlotType } from "@/types/slots/form";
import { getUserInfo } from "@/services/user";
import { findUserByEmail } from "@/models/user";
import { increaseCredits, CreditsTransType } from "@/services/credit";
import { getOneYearLaterTimestr } from "@/lib/time";
import { getTranslations } from "next-intl/server";

export default async function ({ searchParams }: { searchParams: { email?: string } }) {
  const user = await getUserInfo();
  if (!user || !user.uuid) {
    return <Empty message="no auth" />;
  }

  const adminEmails = process.env.ADMIN_EMAILS?.split(",");
  if (!adminEmails?.includes(user?.email)) {
    return <Empty message="Access denied" />;
  }

  // 获取预填充的邮箱参数
  const prefilledEmail = searchParams.email || "";

  // 获取翻译
  const t = await getTranslations("credits.admin");

  // 预先计算所有翻译消息，避免在服务器动作中使用翻译函数
  const translations = {
    gift_title: t("gift_title"),
    gift_description: prefilledEmail ? t("gift_description_with_email", { email: prefilledEmail }) : t("gift_description"),
    gift_breadcrumb: t("gift_breadcrumb"),
    email_label: t("email_label"),
    email_placeholder: t("email_placeholder"),
    email_tip: t("email_tip"),
    amount_label: t("amount_label"),
    amount_placeholder: t("amount_placeholder"),
    amount_tip: t("amount_tip"),
    reason_label: t("reason_label"),
    reason_placeholder: t("reason_placeholder"),
    reason_tip: t("reason_tip"),
    expiration_label: t("expiration_label"),
    expiration_tip: t("expiration_tip"),
    gift_button: t("gift_button"),
    success_message_template: t("success_message"),
  };

  const form: FormSlotType = {
    title: translations.gift_title,
    description: translations.gift_description,
    crumb: {
      items: [
        {
          title: "Users",
          url: "/admin/users",
        },
        {
          title: translations.gift_breadcrumb,
          is_active: true,
        },
      ],
    },
    fields: [
      {
        name: "userEmail",
        title: translations.email_label,
        type: "email",
        placeholder: translations.email_placeholder,
        validation: {
          required: true,
          email: true,
        },
        tip: translations.email_tip,
        value: prefilledEmail,
      },
      {
        name: "credits",
        title: translations.amount_label,
        type: "number",
        placeholder: translations.amount_placeholder,
        validation: {
          required: true,
          min: 1,
          max: 10000,
        },
        tip: translations.amount_tip,
        attributes: {
          min: 1,
          max: 10000,
          step: 1,
        },
      },
      {
        name: "reason",
        title: translations.reason_label,
        type: "textarea",
        placeholder: translations.reason_placeholder,
        validation: {
          required: false,
        },
        tip: translations.reason_tip,
      },
      {
        name: "expiredAt",
        title: translations.expiration_label,
        type: "text",
        placeholder: "2025-12-31T23:59:59.999Z",
        validation: {
          required: false,
        },
        tip: translations.expiration_tip,
      },
    ],
    submit: {
      button: {
        title: translations.gift_button,
        icon: "RiGiftLine",
      },
      handler: async (data: FormData, passby: any) => {
        "use server";

        const userEmail = data.get("userEmail") as string;
        const credits = parseInt(data.get("credits") as string);
        const reason = data.get("reason") as string;
        const expiredAt = data.get("expiredAt") as string;

        // 验证参数
        if (!userEmail || !userEmail.trim()) {
          throw new Error("User email is required");
        }

        if (!credits || credits <= 0 || credits > 10000) {
          throw new Error("Credits must be between 1 and 10000");
        }

        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userEmail)) {
          throw new Error("Invalid email format");
        }

        // 验证过期时间格式（如果提供）
        if (expiredAt && expiredAt.trim()) {
          try {
            const date = new Date(expiredAt);
            if (isNaN(date.getTime())) {
              throw new Error("Invalid expiration date format");
            }
            if (date <= new Date()) {
              throw new Error("Expiration date must be in the future");
            }
          } catch (error) {
            throw new Error("Invalid expiration date format. Use ISO format like: 2025-12-31T23:59:59.999Z");
          }
        }

        try {
          // 查找目标用户
          const targetUser = await findUserByEmail(userEmail.trim());
          if (!targetUser) {
            throw new Error("User not found");
          }

          // 设置过期时间（默认一年后）
          const finalExpiredAt = expiredAt?.trim() || getOneYearLaterTimestr();

          // 直接调用服务器端函数赠送积分
          await increaseCredits({
            user_uuid: targetUser.uuid || "",
            trans_type: CreditsTransType.AdminGift,
            credits: credits,
            expired_at: finalExpiredAt,
            order_no: `admin_gift_form_${user.email}_${Date.now()}`,
          });

          // 记录操作日志
          console.log(`Admin gift credits via form: ${user.email} gifted ${credits} credits to ${userEmail}, reason: ${reason?.trim() || 'Admin gift via form'}`);

          return {
            status: "success",
            message: `Successfully gifted ${credits} credits to ${userEmail}`,
            redirect_url: "/admin/users",
          };

        } catch (error: any) {
          console.error("Gift credits error:", error);
          throw new Error(error.message || "Failed to gift credits");
        }
      },
    },
  };

  return <FormSlot {...form} />;
}
