# 积分系统快速开始指南

## 🚀 5分钟快速上手

### 1. 用户体验流程

#### 查看积分余额
1. 登录网站
2. 点击右上角用户头像
3. 在下拉菜单中查看当前积分数量

#### 生成图片消耗积分
1. 在主页输入图片描述
2. 选择图片数量（1-4张）
3. 开启/关闭高质量选项
4. 查看底部显示的所需积分数量
5. 点击"Generate Image"按钮
6. 系统自动扣除相应积分

#### 积分计算规则
```
所需积分 = 图片数量 × 质量倍数

质量倍数：
- 普通质量：1倍
- 高质量：2倍

示例：
- 1张普通图片 = 1积分
- 1张高质量图片 = 2积分
- 4张高质量图片 = 8积分
```

#### 积分不足处理
1. 当积分不足时，系统会显示详细提示
2. 点击"Purchase Credits"跳转到定价页面
3. 选择合适的积分包进行购买

### 2. 管理员功能

#### 查看用户积分
1. 访问 `/admin/users`
2. 查看用户列表中的积分列
3. 使用搜索框按邮箱查找特定用户

#### 赠送积分
✅ **Gift Credits功能完全正常工作！**

**使用步骤**:
1. 访问 `/admin/users` 页面
2. 点击全局的"🎁 Gift Credits"按钮，或点击用户行的"🎁 Gift"按钮
3. 填写赠送表单：
   - **用户邮箱**：输入目标用户的邮箱地址
   - **积分数量**：输入要赠送的积分数量（1-10000）
   - **原因**：可选，记录赠送原因
   - **过期时间**：可选，ISO格式，默认1年
4. 点击"Gift Credits"按钮提交
5. 系统显示成功消息并跳转到用户列表

**功能特点**:
- ✅ 实时表单验证
- ✅ 邮箱格式验证
- ✅ 积分数量范围验证
- ✅ 自动用户查找
- ✅ 成功反馈提示

#### 查看反馈
1. 访问 `/admin/feedback`
2. 查看用户反馈统计
3. 筛选不同类型和状态的反馈

### 3. 开发者集成

#### 获取用户积分
```typescript
// 前端组件中
import { useCredits } from '@/hooks/useCredits';

function MyComponent() {
  const { credits, loading, error, refresh } = useCredits();
  
  return (
    <div>
      {loading ? '加载中...' : `当前积分: ${credits}`}
    </div>
  );
}
```

#### 扣除积分
```typescript
// API调用示例
const deductCredits = async (credits: number, reason: string) => {
  const response = await fetch('/api/user/credits/deduct', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ credits, reason }),
  });
  
  return response.json();
};
```

#### 检查积分余额
```typescript
// 在生成前检查积分
const checkCreditsBeforeGeneration = async (requiredCredits: number) => {
  const { credits } = await fetch('/api/user/credits').then(r => r.json());
  
  if (credits < requiredCredits) {
    // 显示积分不足对话框
    showInsufficientCreditsDialog(credits, requiredCredits);
    return false;
  }
  
  return true;
};
```

### 4. 常见问题解答

#### Q: 积分显示不更新怎么办？
A: 积分有30秒缓存，可以刷新页面或等待自动更新。

#### Q: 生成失败积分会退还吗？
A: 是的，系统会自动检测生成失败并退还积分。

#### Q: 如何查看积分使用历史？
A: 目前积分历史记录在数据库中，可以通过管理员后台查询。

#### Q: 积分有有效期吗？
A: 是的，积分有有效期，具体时间在购买时确定。

#### Q: 管理员如何批量赠送积分？
A: 目前需要通过API或数据库操作，批量赠送功能在开发中。

### 5. 技术支持

#### 错误排查
1. **积分显示错误**: 检查用户登录状态和API响应
2. **扣除失败**: 查看浏览器控制台错误信息
3. **管理员功能异常**: 确认邮箱在管理员列表中

#### 日志查看
```bash
# 查看应用日志
tail -f logs/application.log

# 查看数据库日志
tail -f logs/database.log

# 查看错误日志
tail -f logs/error.log
```

#### 性能监控
- 积分查询响应时间应 < 200ms
- 积分扣除响应时间应 < 500ms
- 内存缓存命中率应 > 80%

### 6. 最佳实践

#### 用户体验
- 始终显示清晰的积分余额
- 在操作前明确告知积分消耗
- 提供友好的积分不足提示
- 确保积分操作的即时反馈

#### 性能优化
- 使用缓存减少数据库查询
- 批量操作避免N+1问题
- 定期清理过期积分记录
- 监控数据库查询性能

#### 安全考虑
- 验证用户权限
- 防止积分重复扣除
- 记录所有积分操作日志
- 定期审计积分数据

---

**快速指南版本**: v1.0  
**最后更新**: 2025-08-03  
**适用版本**: 积分系统 v1.0
