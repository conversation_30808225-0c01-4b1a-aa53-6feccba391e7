export interface ImageData {
  url: string;
  location?: string;
  bucket?: string;
  key?: string;
  filename: string;
  provider?: string;
  size?: number;
  width?: number;
  height?: number;
}

export interface ImageSlotStatus {
  index: number; // 0-3 for the 4 positions
  status: 'waiting' | 'countdown' | 'generating' | 'completed' | 'failed';
  countdown?: number; // Current countdown value for this slot
  image?: ImageData; // The generated image data when completed
  error?: string; // Error message if failed
  message?: string; // Custom message for the slot (e.g., "预计 30秒 后开始...")
  isActive?: boolean; // Whether this slot is the current focus (for visual highlighting)
}

export interface GenerationSettings {
  ratio?: string;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  imageCount?: string;
  enableHighQuality?: boolean;
  provider?: string;
  model?: string;
}

export interface GenerationResult {
  id: string;
  timestamp: Date;
  prompt: string;
  settings: GenerationSettings;
  images: ImageData[];
  status: 'waiting' | 'generating' | 'completed' | 'failed';
  error?: string;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string; // For image-to-image mode
  countdown?: number; // Current countdown value (deprecated, use imageSlots instead)
  stage?: 'waiting' | 'countdown' | 'generating'; // Current generation stage (deprecated, use imageSlots instead)
  imageSlots?: ImageSlotStatus[]; // Status for each of the 4 image positions
  currentGeneratingIndex?: number; // Index of currently generating image (0-3)
}

export interface GenerationQueue {
  id: string;
  prompt: string;
  settings: GenerationSettings;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string;
  countdown?: number;
}

export interface ImageResultsConfig {
  enableCountdown?: boolean;
  countdownDuration?: number; // in seconds
  maxRetries?: number;
  retryDelay?: number; // in seconds
}
