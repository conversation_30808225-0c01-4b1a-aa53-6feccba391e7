"use client";

import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ImageIcon, 
  PlayCircle, 
  Settings, 
  AlertTriangle, 
  Zap,
  Grid3X3,
  Eye,
  Download
} from "lucide-react";

export default function DemoIndex() {
  const demoPages = [
    {
      title: "Image Results 完整演示",
      description: "展示所有 image-results 组件的各种状态、实时模拟和边缘情况处理",
      href: "/demo/image-results-showcase",
      icon: ImageIcon,
      features: [
        "所有组件状态演示",
        "实时倒计时模拟",
        "边缘情况和错误处理",
        "完整交互功能测试"
      ],
      status: "完整",
      color: "bg-blue-500/20 text-blue-300"
    }
  ];

  const componentFeatures = [
    {
      name: "ImageSlotProgress",
      icon: PlayCircle,
      description: "图片槽进度指示器",
      states: ["waiting", "countdown", "generating", "completed", "failed"],
      features: ["实时倒计时", "进度动画", "状态图标", "错误提示"]
    },
    {
      name: "ImageGrid", 
      icon: Grid3X3,
      description: "4宫格图片展示布局",
      states: ["空白", "部分完成", "全部完成", "混合状态"],
      features: ["响应式布局", "加载状态", "错误处理", "点击交互"]
    },
    {
      name: "ImageResultContainer",
      icon: Settings,
      description: "图片生成结果容器",
      states: ["waiting", "generating", "completed", "failed"],
      features: ["状态徽章", "时间戳", "设置显示", "模式切换"]
    },
    {
      name: "ImagePreviewModal",
      icon: Eye,
      description: "图片预览模态框",
      states: ["单图预览", "多图浏览", "缩略图导航"],
      features: ["键盘导航", "下载功能", "全屏预览", "缩略图"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <div className="max-w-7xl mx-auto space-y-12">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-5xl font-bold text-white mb-4">
            组件演示中心
          </h1>
          <p className="text-white/70 text-xl max-w-3xl mx-auto">
            完整展示 AI 图片生成结果展示系统的所有组件状态、交互效果和边缘情况处理
          </p>
        </div>

        {/* 演示页面导航 */}
        <section>
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            演示页面
          </h2>
          <div className="max-w-2xl mx-auto">
            {demoPages.map((page, index) => {
              const IconComponent = page.icon;
              return (
                <Card key={index} className="bg-white/10 border-white/20 hover:bg-white/15 transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-white/10">
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-white text-xl">
                            {page.title}
                          </CardTitle>
                          <Badge className={page.color}>
                            {page.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-white/70">
                      {page.description}
                    </p>
                    
                    <div className="space-y-2">
                      <h4 className="text-white font-medium text-sm">包含功能:</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {page.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-white/60 text-sm">
                            <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>

                    <Link href={page.href}>
                      <Button className="w-full mt-4">
                        查看演示
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </section>

        {/* 组件功能概览 */}
        <section>
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            组件功能概览
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {componentFeatures.map((component, index) => {
              const IconComponent = component.icon;
              return (
                <Card key={index} className="bg-white/5 border-white/10">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-white/10">
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">
                          {component.name}
                        </CardTitle>
                        <p className="text-white/60 text-sm">
                          {component.description}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="text-white font-medium text-sm mb-2">支持状态:</h4>
                      <div className="flex flex-wrap gap-1">
                        {component.states.map((state, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {state}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-white font-medium text-sm mb-2">核心功能:</h4>
                      <div className="space-y-1">
                        {component.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-white/60 text-sm">
                            <Zap className="w-3 h-3 text-yellow-400" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </section>

        {/* 技术特性 */}
        <section>
          <h2 className="text-3xl font-bold text-white mb-8 text-center">
            技术特性
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-white/5 border-white/10 text-center">
              <CardContent className="pt-6">
                <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-white font-semibold mb-2">实时状态更新</h3>
                <p className="text-white/60 text-sm">
                  支持实时倒计时、进度更新和状态同步
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/5 border-white/10 text-center">
              <CardContent className="pt-6">
                <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Settings className="w-6 h-6 text-green-400" />
                </div>
                <h3 className="text-white font-semibold mb-2">灵活配置</h3>
                <p className="text-white/60 text-sm">
                  支持多种生成模式和参数配置
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/5 border-white/10 text-center">
              <CardContent className="pt-6">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="text-white font-semibold mb-2">错误处理</h3>
                <p className="text-white/60 text-sm">
                  完善的错误处理和用户友好的提示
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* 使用说明 */}
        <section className="text-center">
          <Card className="bg-white/5 border-white/10 max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="text-white text-2xl">
                使用说明
              </CardTitle>
            </CardHeader>
            <CardContent className="text-white/70 space-y-4">
              <p>
                这个演示系统展示了完整的 AI 图片生成结果展示组件库的所有功能和状态。
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <div>
                  <h4 className="text-white font-medium mb-2">基础演示包含:</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• 所有组件的标准状态</li>
                    <li>• 完整的用户交互流程</li>
                    <li>• 图片预览和下载功能</li>
                    <li>• 多种生成模式展示</li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-white font-medium mb-2">高级演示包含:</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• 实时状态模拟</li>
                    <li>• 边缘情况处理</li>
                    <li>• 各种错误状态</li>
                    <li>• 性能压力测试</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
