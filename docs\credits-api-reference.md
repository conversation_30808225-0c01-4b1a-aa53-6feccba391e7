# 积分系统 API 参考文档

## 📋 API 概述

积分系统提供RESTful API接口，支持积分查询、扣除、退还等核心功能。所有API都需要用户认证。

## 🔐 认证方式

所有API请求都需要在Header中包含认证信息：

```http
Authorization: Bearer <jwt_token>
```

## 📊 用户积分API

### 获取用户积分余额

获取当前登录用户的积分余额信息。

```http
GET /api/user/credits
```

#### 响应示例
```json
{
  "success": true,
  "credits": 150,
  "message": "Credits retrieved successfully"
}
```

#### 响应字段
| 字段 | 类型 | 描述 |
|------|------|------|
| success | boolean | 请求是否成功 |
| credits | number | 当前可用积分数量 |
| message | string | 响应消息 |

#### 错误响应
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "User not authenticated"
}
```

### 扣除用户积分

从用户账户中扣除指定数量的积分。

```http
POST /api/user/credits/deduct
Content-Type: application/json
```

#### 请求体
```json
{
  "credits": 2,
  "reason": "Image generation",
  "metadata": {
    "imageCount": 1,
    "highQuality": true,
    "prompt": "A beautiful sunset"
  }
}
```

#### 请求字段
| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| credits | number | 是 | 要扣除的积分数量 |
| reason | string | 是 | 扣除原因 |
| metadata | object | 否 | 附加元数据 |

#### 响应示例
```json
{
  "success": true,
  "remainingCredits": 148,
  "transactionId": "txn_1234567890",
  "message": "Credits deducted successfully"
}
```

#### 响应字段
| 字段 | 类型 | 描述 |
|------|------|------|
| success | boolean | 操作是否成功 |
| remainingCredits | number | 扣除后剩余积分 |
| transactionId | string | 交易ID |
| message | string | 响应消息 |

#### 错误响应
```json
{
  "success": false,
  "error": "InsufficientCredits",
  "message": "Not enough credits",
  "currentCredits": 1,
  "requiredCredits": 2
}
```

### 退还用户积分

向用户账户退还积分，通常用于操作失败的情况。

```http
POST /api/user/credits/refund
Content-Type: application/json
```

#### 请求体
```json
{
  "credits": 2,
  "reason": "Generation failed",
  "originalTransactionId": "txn_1234567890"
}
```

#### 请求字段
| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| credits | number | 是 | 要退还的积分数量 |
| reason | string | 是 | 退还原因 |
| originalTransactionId | string | 否 | 原始交易ID |

#### 响应示例
```json
{
  "success": true,
  "newBalance": 150,
  "refundTransactionId": "refund_1234567890",
  "message": "Credits refunded successfully"
}
```

## 👨‍💼 管理员API

### 赠送积分给用户

管理员向指定用户赠送积分。

```http
POST /api/admin/credits/gift
Content-Type: application/json
```

#### 请求体
```json
{
  "userEmail": "<EMAIL>",
  "credits": 100,
  "reason": "Welcome bonus",
  "expiredAt": "2025-12-31T23:59:59Z"
}
```

#### 请求字段
| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| userEmail | string | 是 | 目标用户邮箱 |
| credits | number | 是 | 赠送积分数量 |
| reason | string | 否 | 赠送原因 |
| expiredAt | string | 否 | 过期时间(ISO格式) |

#### 响应示例
```json
{
  "success": true,
  "userUuid": "user_uuid_123",
  "giftTransactionId": "gift_1234567890",
  "message": "Credits gifted successfully"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": "UserNotFound",
  "message": "User <NAME_EMAIL> not found"
}
```

### 获取用户积分历史

管理员查看指定用户的积分使用历史。

```http
GET /api/admin/credits/history?userEmail=<EMAIL>&limit=50&offset=0
```

#### 查询参数
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| userEmail | string | 是 | 用户邮箱 |
| limit | number | 否 | 返回记录数量(默认50) |
| offset | number | 否 | 偏移量(默认0) |

#### 响应示例
```json
{
  "success": true,
  "history": [
    {
      "id": 123,
      "credits": -2,
      "transType": "ImageGeneration",
      "orderNo": "order_123",
      "createdAt": "2025-08-03T10:30:00Z",
      "expiredAt": "2025-12-31T23:59:59Z"
    }
  ],
  "total": 1,
  "currentBalance": 148
}
```

## 🔍 状态码说明

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 📝 错误代码

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| Unauthorized | 用户未认证 | 检查认证token |
| InsufficientCredits | 积分不足 | 提示用户购买积分 |
| UserNotFound | 用户不存在 | 检查用户邮箱 |
| InvalidCreditsAmount | 积分数量无效 | 检查积分数量范围 |
| TransactionFailed | 交易失败 | 重试或联系技术支持 |

## 🧪 API测试示例

### 使用curl测试

```bash
# 获取积分余额
curl -H "Authorization: Bearer <token>" \
     https://your-domain.com/api/user/credits

# 扣除积分
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <token>" \
     -d '{"credits":2,"reason":"Image generation"}' \
     https://your-domain.com/api/user/credits/deduct

# 退还积分
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <token>" \
     -d '{"credits":2,"reason":"Generation failed"}' \
     https://your-domain.com/api/user/credits/refund
```

### 使用JavaScript测试

```javascript
// 获取积分
const getCredits = async () => {
  const response = await fetch('/api/user/credits', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// 扣除积分
const deductCredits = async (credits, reason) => {
  const response = await fetch('/api/user/credits/deduct', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ credits, reason })
  });
  return response.json();
};

// 退还积分
const refundCredits = async (credits, reason) => {
  const response = await fetch('/api/user/credits/refund', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ credits, reason })
  });
  return response.json();
};
```

## 📊 性能指标

### 响应时间要求
- 积分查询: < 200ms
- 积分扣除: < 500ms
- 积分退还: < 500ms
- 管理员操作: < 1000ms

### 并发限制
- 每用户每秒最多10次积分操作
- 管理员API每秒最多100次请求
- 全局API每秒最多1000次请求

## 🔄 版本更新

### v1.0 (当前版本)
- 基础积分查询、扣除、退还功能
- 管理员赠送积分功能
- 积分历史查询功能

### 计划功能
- 批量积分操作API
- 积分统计分析API
- 积分有效期管理API
- 积分兑换API

---

**API文档版本**: v1.0  
**最后更新**: 2025-08-03  
**兼容版本**: 积分系统 v1.0
